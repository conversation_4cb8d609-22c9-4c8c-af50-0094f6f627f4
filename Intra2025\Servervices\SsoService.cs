﻿using Intra2025.Components.Base;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using OAKsIlanAppUser.OAKsIlanAppUser;
using OAKsIlanOrgTree.OAKsIlanOrgTree;
using OAKsIlanApp.OAKsIlanApp;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.AspNetCore.Components;


namespace Intra2025.Servervices
{
    public class SsoService
    {
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly ILogger<SsoService> _logger;
        private UserState _userState;
        private readonly IWebHostEnvironment _env;
        private readonly IMemoryCache _memoryCache;
        private readonly NavigationManager _navManager;
        private readonly IConfiguration _configuration;


        public SsoService(IHttpContextAccessor httpContextAccessor, UserState userState, ILogger<SsoService> logger,
                IWebHostEnvironment env, IMemoryCache memoryCache, NavigationManager navManager, IConfiguration configuration)
        {
            _httpContextAccessor = httpContextAccessor;
            _userState = userState;
            _logger = logger;
            _env = env;
            _memoryCache = memoryCache;
            _navManager = navManager;
            _configuration = configuration;
        }

        //1、判斷是否有登入 2、是否為管理者 3、允許哪些單位(使用者)可以登入 4、初始化UserState
        public void ValidateAndInitializeUser()
        {
            if (!string.IsNullOrEmpty(_userState.Account))
            {
                // 已經初始化，直接返回
                return;
            }

            // 檢查是否為開發環境且啟用 SSO 繞過
            if (_env.IsDevelopment() && _configuration.GetValue<bool>("SsoSettings:BypassSso"))
            {
                InitializeTestUser();
                return;
            }

            var httpContext = _httpContextAccessor.HttpContext;
            // _logger.LogInformation("UserState_{acc} ::: {dt} ", _userState.Account, DateTime.Now.ToString());

            // 檢查 Cookies 是否存在
            if (httpContext?.Request?.Cookies["PUBLIC_APP_USER_SSO_TOKEN"] == null)
            {
                // 無 Cookies，重定向到指定 URL
                httpContext?.Response.Redirect("https://eip.e-land.gov.tw", false);
                return;
            }

            // 有 Cookies，取出 SSO Token
            string? publicAppUserSsoToken = "";
            publicAppUserSsoToken = System.Net.WebUtility.HtmlEncode(httpContext?.Request?.Cookies["PUBLIC_APP_USER_SSO_TOKEN"]);

            JObject ResponseOrgObj = GetUserBasicProfileFromApi(publicAppUserSsoToken ?? "default_token");

            // _logger.LogInformation("API 獲取使用者基本資料");
            if (ResponseOrgObj["ERROR_CODE"]?.ToString() == "0")
            {
                var basicProfile = ResponseOrgObj["APP_USER_BASIC_PROFILE"] as JObject;
                var employProfile = (JObject?)ResponseOrgObj["APP_USER_EMPLOY_PROFILE"] ?? new JObject();
                bool isAdmin = false;

                var jsonDataLoader = new JsonDataLoader(_env);

                // 改用 NavigationManager 獲得正確路徑
                var uri = new Uri(_navManager.Uri);
                var currentPath = uri.AbsolutePath;
                // 判斷是否為 YCRS 功能頁面
                // 使用 StartsWith 來判斷路徑是否以指定的字串開頭
                bool isYcrsSystem = currentPath.StartsWith("/ChildCase", StringComparison.OrdinalIgnoreCase)
                                 || currentPath.StartsWith("/CareCase", StringComparison.OrdinalIgnoreCase)
                                 || currentPath.StartsWith("/YCRS", StringComparison.OrdinalIgnoreCase);
                if (isYcrsSystem)
                {
                    // 從 JSON 載入的管理者帳號列表
                    List<string> Admins = jsonDataLoader.GetAdminAccounts();
                    // 判斷是否為 Admin
#pragma warning disable CS8602 // Dereference of a possibly null reference.
                    isAdmin = Admins.Contains(basicProfile["APP_USER_LOGIN_ID"]?.ToString() ?? string.Empty);
#pragma warning restore CS8602 // Dereference of a possibly null reference.

                    // 只有【12間戶所】、【戶政科】、【社會處-兒少及婦女福利科】、【資管科】可登入
                    string[] allowUse = ["311", "312", "313", "314", "315", "316", "317", "318", "319", "320", "321", "322", "102008", "110015", "113007"];
                    // 從 basicProfile 中取值
                    string parentDeptCode = basicProfile["org_code"]?.ToString() ?? string.Empty;
                    string l1DeptCode = basicProfile["parent_dept_code"]?.ToString() ?? string.Empty;
                    string userLoginId = basicProfile["APP_USER_LOGIN_ID"]?.ToString() ?? string.Empty;

                    bool isAllowed = allowUse.Contains(l1DeptCode) ||
                     allowUse.Contains(userLoginId) ||
                     allowUse.Contains(parentDeptCode);

                    if (!isAllowed)
                    {
                        // 非屬允許的清單就導入至入口網
                        httpContext?.Response.Redirect("https://eip.e-land.gov.tw", false);
                        return;
                    }
                }
                // 初始化 UserState
                _userState.Initialize(
                    basicProfile["APP_USER_LOGIN_ID"]?.ToString() ?? string.Empty,
                    basicProfile["APP_USER_CHT_NAME"]?.ToString() ?? string.Empty,
                    basicProfile["org_code"]?.ToString() ?? string.Empty, //UnitCode (例如: 117)
                    basicProfile["l1_dept_name"]?.ToString() ?? string.Empty,
                    basicProfile["parent_dept_code"]?.ToString() ?? string.Empty, //DepCode (例如: 113007)
                    basicProfile["parent_dept_name"]?.ToString() ?? string.Empty,
                    employProfile["APP_USER_EMPLOY_TITLE"]?.ToString() ?? string.Empty,
                    isAdmin
                );

                //_logger.LogInformation("Start SaveUserStateToSession......................... ");
                //_logger.LogInformation("SsoService UserState Reference: {Reference}", _userState.GetHashCode());

            }
            else
            {
                // 若獲取資料失敗，也可重定向或處理其他邏輯
                Console.WriteLine("SSO 資料初始化失敗11: " + ResponseOrgObj["ERROR_CODE"]);
                //  _logger.LogInformation("SSO. 資料初始化失敗11: " + publicAppUserSsoToken + ResponseOrgObj["ERROR_CODE"]);
            }
        }

        //取得使用者基本資料(From EIP API)
        private JObject GetUserBasicProfileFromApi(string publicAppUserSsoToken)
        {
            // 設定主機訊息
            string api_url = "https://eipapi.e-land.gov.tw";
            string app_private_id = "abeaa0a5d3d64312aa0c3f86903806db"; //舊公文系統的暫用id
            string app_private_passwd = "d510156e88f447a7a5ec40da486ae2de"; //舊公文系統的暫用passwd

            try
            {
                // 2-1. 應用系統身分認證
                var authObj = new JObject
        {
            { "APP_PRIVATE_ID", app_private_id },
            { "APP_PRIVATE_PASSWD", app_private_passwd }
        };

                app lanAppAuth = new app(api_url);
                var authResponse = lanAppAuth.call_ws("WS-Z01-A0-01", authObj);

                if (authResponse["ERROR_CODE"]?.ToString() != "0")
                {
                    throw new Exception("身分認證失敗: " + authResponse["ERROR_CODE"]);
                }

                string privilegedAppSsoToken = authResponse["PRIVILEGED_APP_SSO_TOKEN"]?.ToString() ?? string.Empty;

                // 2-2. 取得使用者內部代碼物件
                var userRequestObj = new JObject
        {
            { "PRIVILEGED_APP_SSO_TOKEN", privilegedAppSsoToken },
            { "PUBLIC_APP_USER_SSO_TOKEN_TO_QUERY", publicAppUserSsoToken }
        };

                app_user lanAppUser = new app_user(api_url);
                var userResponse = lanAppUser.call_ws("WS-Z01-B0-06", userRequestObj);

                if (userResponse["ERROR_CODE"]?.ToString() != "0")
                {
                    throw new Exception("使用者內部代碼認證失敗: " + userResponse["ERROR_CODE"]);
                }

                string appCompyUuid = userResponse["APP_COMPANY_UUID"]?.ToString() ?? string.Empty;
                string appUserNodeUuid = userResponse["APP_USER_NODE_UUID"]?.ToString() ?? string.Empty;

                // 2-3. 取得單一使用者節點屬性
                string orgStr = (string)GenerateJsonQuestString(privilegedAppSsoToken, publicAppUserSsoToken, appCompyUuid, appUserNodeUuid);
                var orgRequestObj = JsonConvert.DeserializeObject<JObject>(orgStr);

                org_tree lanOrgTree = new org_tree(api_url);
                var orgResponse = lanOrgTree.call_ws("WS-Z01A-D-B05", orgRequestObj);

                if (orgResponse["ERROR_CODE"]?.ToString() != "0")
                {
                    throw new Exception("組織樹查詢錯誤: " + orgResponse["ERROR_CODE"]);
                }

                // 返回完整的響應，包含 APP_USER_BASIC_PROFILE 和 APP_USER_EMPLOY_PROFILE
                return orgResponse;
            }
            catch (Exception ex)
            {
                Console.WriteLine("GetUserBasicProfileFromApi 發生錯誤: " + ex.Message);
                _logger.LogWarning("GetUserBasicProfileFromApi 發生錯誤: " + ex.Message);
                return new JObject { ["ERROR_CODE"] = "1", ["ERROR_MESSAGE"] = ex.Message };
            }
        }

        public object GenerateJsonQuestString(string sso_token, string user_sso_token, string company_uuid, string user_uuid)
        {
            StringWriter sw = new StringWriter();
            JsonTextWriter writer = new JsonTextWriter(sw);
            writer.WriteStartObject();
            writer.WritePropertyName("PRIVILEGED_APP_SSO_TOKEN");
            writer.WriteValue(sso_token);
            writer.WritePropertyName("APP_COMPANY_UUID");
            writer.WriteValue(company_uuid);
            writer.WritePropertyName("PUBLIC_APP_USER_SSO_TOKEN");
            writer.WriteValue(user_sso_token);
            writer.WritePropertyName("APP_USER_NODE_UUID");
            writer.WriteValue(user_uuid);
            writer.WritePropertyName("APP_USER_BASIC_PROFILE");
            writer.WriteStartObject();
            // =指定回傳 用戶帳號
            writer.WritePropertyName("APP_USER_LOGIN_ID");
            writer.WriteValue("");
            // =指定回傳 員工編號
            writer.WritePropertyName("APP_USER_EMPNO");
            writer.WriteValue("");
            // =指定回傳 用戶姓名
            writer.WritePropertyName("APP_USER_CHT_NAME");
            writer.WriteValue("");
            writer.WritePropertyName("APP_USER_ENG_NAME");
            writer.WriteValue("");
            // =指定回傳 用戶電子郵件
            writer.WritePropertyName("APP_USER_EMAIL");
            writer.WriteValue("");
            // ====指定回傳 用戶其他屬性資訊
            writer.WritePropertyName("APP_USER_OFFICE_PHONE_NO");
            writer.WriteValue("");
            writer.WritePropertyName("APP_USER_MOBILE_PHONE_NO");
            writer.WriteValue("");
            writer.WritePropertyName("APP_USER_OFFICE_FAX_NO");
            writer.WriteValue("");
            writer.WritePropertyName("APP_USER_MFP_CARD_NO");
            writer.WriteValue("");
            writer.WritePropertyName("APP_USER_OFFICE_ADDRESS");
            writer.WriteValue("");
            writer.WritePropertyName("APP_USER_OFFICE_ZIP_CODE");
            writer.WriteValue("");
            // ======
            // =指定回傳用戶狀態 1: 啟用 0: 停用 (包含 差勤離職、差勤留職停薪、差勤借調、差勤刪除、差勤病故、差勤退休)
            writer.WritePropertyName("APP_USER_STATUS");
            writer.WriteValue("");
            writer.WritePropertyName("APP_USER_NODE_LAST_UPDATE_TIME");
            writer.WriteValue("");
            writer.WritePropertyName("APP_USER_NODE_LAST_UPDATE_TAG");
            writer.WriteValue("");
            // =指定回傳 用戶內部部門代碼
            writer.WritePropertyName("APP_DEPT_NODE_UUID");
            writer.WriteValue("");
            // ===指定回傳用戶所屬機關、所屬單位、直屬單位資訊==
            // =指定回傳 機關代碼
            writer.WritePropertyName("org_code");
            writer.WriteValue("");
            // =指定回傳 機關名稱
            writer.WritePropertyName("org_name");
            writer.WriteValue("");
            // =指定回傳 所屬機關代碼
            writer.WritePropertyName("l1_dept_code");
            writer.WriteValue("");
            // =指定回傳 所屬機關名稱
            writer.WritePropertyName("l1_dept_name");
            writer.WriteValue("");
            // =指定回傳 直屬機關代碼
            writer.WritePropertyName("parent_dept_code");
            writer.WriteValue("");
            // =指定回傳 直屬機關名稱
            writer.WritePropertyName("parent_dept_name");
            writer.WriteValue("");
            // =======
            writer.WriteEndObject();
            writer.WritePropertyName("APP_USER_HR_PROFILE");
            writer.WriteStartObject();
            writer.WritePropertyName("APP_USER_PASSPORT_NO");
            writer.WriteValue("");
            // =指定回傳 用戶身分證資訊，不一定指完整
            writer.WritePropertyName("APP_USER_TW_SSN");
            writer.WriteValue("");
            // =指定回傳 用戶性別
            writer.WritePropertyName("APP_USER_GENDER");
            writer.WriteValue("");
            // =指定回傳 用戶生日
            writer.WritePropertyName("APP_USER_BIRTHDATE");
            writer.WriteValue("");
            writer.WritePropertyName("APP_USER_NODE_LAST_UPDATE_TIME");
            writer.WriteValue("");
            writer.WritePropertyName("APP_USER_NODE_LAST_UPDATE_TAG");
            writer.WriteValue("");
            writer.WriteEndObject();
            // =使用者工作資料表
            writer.WritePropertyName("APP_USER_EMPLOY_PROFILE");
            writer.WriteStartObject();
            // ==指定回傳職稱
            writer.WritePropertyName("APP_USER_EMPLOY_TITLE");
            writer.WriteValue("");
            writer.WriteEndObject();
            writer.WriteEndObject();
            return sw.ToString();
        }

        /// <summary>
        /// 初始化測試用戶（僅用於開發環境）
        /// </summary>
        private void InitializeTestUser()
        {
            var testUserSection = _configuration.GetSection("SsoSettings:TestUser");

            _userState.Account = testUserSection.GetValue<string>("Account") ?? "testuser";
            _userState.UserName = testUserSection.GetValue<string>("Name") ?? "測試用戶";
            _userState.UnitName = testUserSection.GetValue<string>("Department") ?? "資訊室";
            _userState.DepName = testUserSection.GetValue<string>("Department") ?? "資訊室";
            _userState.IsAdmin = testUserSection.GetValue<bool>("IsAdmin");
            _userState.IsInitialized = true;

            _logger.LogInformation("開發環境：已初始化測試用戶 - 帳號: {Account}, 姓名: {UserName}",
                _userState.Account, _userState.UserName);
        }
    }

}
