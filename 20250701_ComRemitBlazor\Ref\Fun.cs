using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using iTextSharp.text;
using iTextSharp.text.pdf;
using System.IO;
using YuMingClass;
using System.Data;
using System.Net;
using System.Text;

/// <summary>
/// Summary description for Func
/// </summary>
public class Func
{

    public Func()
    {
        //
        // TODO: Add constructor logic here
        //
    }
    //######################郵局報表區Start###################################
    //薪資總表start
    public void ExportPostPDF(string sn)   //匯出【郵局-薪資總表】pdf檔
    {
        Dictionary<string, object> Dic = new Dictionary<string, object>();
        Dic["@id"] = sn;
        YuMingClass.DataFunc myfunc = new DataFunc();

        string errorMsg = "";
        if (Convert.ToInt16(myfunc.DBGetOneRec("ComRemitConnectionString", "select count(*) from RemitedList where ConSno=@id and len(CollecAcc)>14", Dic)) > 0)
        {
            DataTable dt = myfunc.DBGetDataTable("ComRemitConnectionString", "select CollecName from RemitedList where ConSno=@id and len(CollecAcc)>14", Dic);
            int t = 0;
            while (t < dt.Rows.Count)
            {
                errorMsg = errorMsg + " " + dt.Rows[t]["CollecName"].ToString();
                t++;
            }
            HttpContext.Current.Response.Write("您所彙整的身份證字號格式不符(須為14碼，請確認其長度、或有無空白)，有問題人員為:(" + errorMsg + ")");
            HttpContext.Current.Response.End();
        }



        int totalAmount = 0; //總筆數
        int totalCost = 0;   //總金額
        int totalPages = 0;  //總頁數
        string Filename = "CR" + sn.PadLeft(6, '0') + "_Post";
        MemoryStream memory = new MemoryStream();
        var doc = new Document(PageSize.A4, 60, 50, 30, 50); //L R U B
        PdfWriter pdfw = PdfWriter.GetInstance(doc, memory);
        BaseFont bf = BaseFont.CreateFont(@"C:\Windows\Fonts\kaiu.ttf", BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);
        Font ChFontHead = new Font(bf, 18, Font.BOLD);
        Font ChFont = new Font(bf, 11, Font.NORMAL);
        string memo = "";

        doc.Open();
        for (int x = 0; x < 3; x++) //一式三份
        {
            //#######Gen BarCode#############        
            PdfContentByte cb = pdfw.DirectContent;
            Paragraph pa = new Paragraph();

            Image barcodeImage = genbarCode(Filename, cb);

            pa.Add(barcodeImage);
            doc.Add(pa);
            pa.Clear();

            DataTable dt = myfunc.DBGetDataTable("ComRemitConnectionString", "select * from RemitedList where ConSno=@id ORDER BY sno", Dic);

            while (totalAmount < dt.Rows.Count)
            {
                totalCost = totalCost + Convert.ToInt32(dt.Rows[totalAmount]["RemitPrice"]);
                totalAmount++;
            }
            totalPages = (int)Math.Ceiling(Convert.ToDouble(totalAmount) / 20); //總頁數
            memo = dt.Rows[0]["ConMemo"].ToString(); //備註
            //Font ChFontBold = new Font(bf, 12, Font.BOLD);
            Dic["@Sn"] = sn;
            //標頭
            Chunk c = new Chunk("委託郵局代存員工薪資總表(408)\n\n", ChFontHead);
            Phrase p0 = new Phrase(c);
            Paragraph pg0 = new Paragraph(p0);
            pg0.Alignment = 1; //0:left 1:center 2:right
            doc.Add(pg0);

            //本文開始 產生表格 -- START       
            PdfPTable pdfTab = new PdfPTable(new float[] { 1, 1, 1 }); // 建立3個欄位表格之相對寬度
            pdfTab.TotalWidth = 520f; // 表格總寬
            pdfTab.LockedWidth = true;
            // 首頁【408總表】塞入資料 -------------------------- START         
            //-----第一列
            PdfPCell pc = new PdfPCell(new Phrase("\n受託局名: 宜蘭中山路郵局\n\n受託局號: 0111000\n\n檔名: " + Filename, ChFont));
            pc.Border = Rectangle.NO_BORDER;  //設為無邊框
            pdfTab.AddCell(pc); //寫入第1列第1個cell   
            pc = new PdfPCell(new Phrase("\n劃撥儲金帳號:********\n\n\n押碼值:", ChFont));
            pc.Border = Rectangle.NO_BORDER;  //設為無邊框        
                                              //pc.AddElement(new Phrase("押碼值:", ChFont));
            pdfTab.AddCell(pc); //寫入第1列第2個cell  
            PdfPTable pdfTabtmp;
            genRow1Cell3(ChFont, out pc, out pdfTabtmp);
            pc = new PdfPCell(pdfTabtmp);
            pdfTab.AddCell(pc);  //寫入第1列第3個cell       

            pc = new PdfPCell(new Phrase("\n\n", ChFont));
            pc.Border = Rectangle.NO_BORDER;  //設為無邊框 
            pc.Colspan = 3;
            pdfTab.AddCell(pc); //寫入空列
                                //-----第二列
            genRow2(ChFont, out pc, out pdfTabtmp, totalCost, totalAmount, memo);
            pc = new PdfPCell(pdfTabtmp);
            pc.Colspan = 3;
            pdfTab.AddCell(pc); //寫入第2列第1個cell  
                                //-----第三列
            string strRow3 = "\n委託機構名稱: 宜蘭縣政府\n委託機構地址: 宜蘭市縣政北路1號\n連絡    電話: " + HttpContext.Current.Session["tel"] + "\n傳真    號碼: " + HttpContext.Current.Session["fax"] + "\n經辦:                               單位主管:";
            pc = new PdfPCell(new Phrase(strRow3, ChFont));
            pc.Border = Rectangle.NO_BORDER;  //設為無邊框
            pc.Colspan = 3;
            pdfTab.AddCell(pc); //寫入第3列第1個cell     
                                //-----第四列
            pc = new PdfPCell(new Phrase("", ChFont));
            pc.Border = Rectangle.NO_BORDER;  //設為無邊框
            pdfTab.AddCell(pc); //寫入第4列第1個cell   
            pc = new PdfPCell(new Phrase("", ChFont));
            pc.Border = Rectangle.NO_BORDER;  //設為無邊框 
            pdfTab.AddCell(pc); //寫入第4列第2個cell 
            genRow4Cell3(ChFont, out pc, out pdfTabtmp);
            pc = new PdfPCell(pdfTabtmp);
            pdfTab.AddCell(pc);  //寫入第4列第3個cell       
                                 //-----第五列
            pc = new PdfPCell(new Phrase("", ChFont));
            pc.Border = Rectangle.NO_BORDER;  //設為無邊框
            pdfTab.AddCell(pc); //寫入第4列第1個cell   
            pc = new PdfPCell(new Phrase("", ChFont));
            pc.Border = Rectangle.NO_BORDER;  //設為無邊框 
            pdfTab.AddCell(pc); //寫入第4列第2個cell   
            pc = new PdfPCell(new Phrase("儲匯壽險專用章\n主管:__________", ChFont));
            pc.Border = Rectangle.NO_BORDER;  //設為無邊框
            pdfTab.AddCell(pc); //寫入第4列第3個cell 
                                //-----第六列
            string strRow6 = "\n說明:\n1.本表由委託機構於每月撥存員工薪資時填造一式三份檢附相關團體戶存款單及支票，一併交受託局(以薪資存款直接傳輸作業者，僅需填一份，免造送存款單)。\n2.本表各項目請正確填寫清楚，並與附件支票及團體戶存款單核對無誤。\n3.委託機構應將撥存總額開具劃線支票一張，在本表備註欄註明付款行庫名稱、帳號及支票號碼。\n4.受託局受理時在一份總表指定處加蓋主管及經辦員章與郵戳作為收款及收件之依據，不另備文或另開收據。\n5.本表右上角薪資轉存「資料別及作業方式」、「批次」等欄務必勾填。\n6.薪資存款入帳後，受託局應將留局之薪資總表隨「現金及票卷日報」送會計單位審核後，退回存查。";
            pc = new PdfPCell(new Phrase(strRow6, ChFont));
            pc.Border = Rectangle.NO_BORDER;  //設為無邊框
            pc.Colspan = 3;
            pdfTab.AddCell(pc); //寫入第6列第1個cell     
            pc = new PdfPCell(new Phrase("\n\n", ChFont));
            pc.Border = Rectangle.NO_BORDER;  //設為無邊框 
            pc.Colspan = 3;
            pdfTab.AddCell(pc); //寫入空列
                                //-----第七列
            genRow7(ChFont, out pc, out pdfTabtmp);
            pc = new PdfPCell(pdfTabtmp);
            pc.Colspan = 3;
            pdfTab.AddCell(pc); //寫入第7列第1個cell  
                                //-----空列
            pc = new PdfPCell(new Phrase("\n\n\n", ChFont));
            pc.Border = Rectangle.NO_BORDER;  //設為無邊框 
            pc.Colspan = 3;
            pdfTab.AddCell(pc); //寫入空列
            doc.Add(pdfTab); //將資料寫進文件中          
                             // 首頁【408總表】塞入資料 -------------------------- END

            doc.NewPage(); //強制換頁


            //【明細本文】開始 產生表格 -- START **********************************
            //-----第一列  
            //genDetailPageHeader(doc, ChFontHead, ChFont, out pdfTab, out pc, out pdfTabtmp);



            //-----第三列 (資料列)
            int countPerPage = 30; //一頁要列印的筆數
            int startR = 0;
            int endR = dt.Rows.Count;

            //if (dt.Rows.Count / countPerPage > 0)
            //{  //一頁中的明細若超過35筆時，強制分頁!!
            int xx = dt.Rows.Count / countPerPage;//控制要分幾頁列印
            int i = 1;
            while (i <= xx)
            {
                startR = (i - 1) * countPerPage;
                endR = startR + countPerPage;
                barcodeImage = genbarCode(Filename + "-" + i, cb);
                pa.Add(barcodeImage); //表頭加入barcode
                doc.Add(pa);
                pa.Clear();
                genDetailPageHeader(doc, ChFontHead, ChFont, out pdfTab, out pc, out pdfTabtmp);

                pdfTab = new PdfPTable(new float[] { 1, 1, 4, 3 }); // 建立4個欄位表格之相對寬度        
                pdfTab.TotalWidth = 520f; // 表格總寬
                pdfTab.LockedWidth = true;
                genRow3(out pc, out pdfTabtmp, dt, totalCost, totalAmount, startR, endR, false);
                pc = new PdfPCell(pdfTabtmp);
                pc.Colspan = 4;
                pdfTab.AddCell(pc);
                doc.Add(pdfTab);
                doc.NewPage(); //換頁
                i++;
            }
            if (dt.Rows.Count % countPerPage > 0)//印最後一頁
            {
                startR = xx * countPerPage;
                endR = startR + (dt.Rows.Count % countPerPage);

                barcodeImage = genbarCode(Filename + "-" + (xx + 1), cb);
                pa.Add(barcodeImage); //表頭加入barcode
                doc.Add(pa);
                pa.Clear();
                genDetailPageHeader(doc, ChFontHead, ChFont, out pdfTab, out pc, out pdfTabtmp);

                pdfTab = new PdfPTable(new float[] { 1, 1, 4, 3 }); // 建立4個欄位表格之相對寬度        
                pdfTab.TotalWidth = 520f; // 表格總寬
                pdfTab.LockedWidth = true;
                genRow3(out pc, out pdfTabtmp, dt, totalCost, totalAmount, startR, endR, false);
                pc = new PdfPCell(pdfTabtmp);
                pc.Colspan = 4;
                pdfTab.AddCell(pc);
                doc.Add(pdfTab);
                //doc.NewPage(); //換頁
            }
            //}
            //else
            //    genRow3(out pc, out pdfTabtmp, dt, totalCost, totalAmount, startR, endR);

            //pa.Add(barcodeImage); //表頭加入barcode
            //doc.Add(pa);
            //pa.Clear();
            //pdfTab = new PdfPTable(new float[] { 1, 1, 4, 3 }); // 建立4個欄位表格之相對寬度        
            //pdfTab.TotalWidth = 520f; // 表格總寬
            //pdfTab.LockedWidth = true;
            //genRow3(out pc, out pdfTabtmp, dt, totalCost, totalAmount, 0, 40,false);
            //pc = new PdfPCell(pdfTabtmp);
            //pc.Colspan = 4;
            //pdfTab.AddCell(pc);
            //doc.Add(pdfTab);
            //doc.NewPage(); //換頁

            //pa.Add(barcodeImage); //表頭加入barcode
            //doc.Add(pa);
            //pa.Clear();
            //pdfTab = new PdfPTable(new float[] { 1, 1, 4, 3 });
            //pdfTab.TotalWidth = 520f; // 表格總寬
            //pdfTab.LockedWidth = true;
            //genRow3(out pc, out pdfTabtmp, dt, totalCost, totalAmount, 40, 42, true);

            //pc = new PdfPCell(pdfTabtmp);
            //pc.Colspan = 4;
            //pdfTab.AddCell(pc);
            //doc.Add(pdfTab);

            pdfTab = new PdfPTable(new float[] { 1, 1, 4, 3 }); // 建立4個欄位表格之相對寬度        
            pdfTab.TotalWidth = 520f; // 表格總寬
            pdfTab.LockedWidth = true;
            genRow4(out pc, out pdfTabtmp, dt, totalCost, totalAmount); //最後小計、總計區
            pdfTab.AddCell(pc);
            doc.Add(pdfTab);


            pc = new PdfPCell(pdfTabtmp);
            pc.Colspan = 4;
            pdfTab.AddCell(pc);

            //-----第四列 
            pc = new PdfPCell(new Phrase("\n\n委託機構名稱:宜蘭縣政府    經辦:________________________________   主管:____________  \n\n委託機構地址:260宜蘭市縣政北路1號       連絡電話:___________\n", ChFont));
            pc.Colspan = 4;
            pc.Border = Rectangle.NO_BORDER;  //設為無邊框
            pdfTab.AddCell(pc); //寫入第4列   
                                //-----第五列 
            pc = new PdfPCell(new Phrase("\n(非連線)\n立帳局郵戳", ChFont));
            pc.Border = Rectangle.NO_BORDER;  //設為無邊框
            pc.HorizontalAlignment = 2;//0:left 1:center 2:right
            pc.Colspan = 2;
            pdfTab.AddCell(pc); //寫入第5列第1~2個cell

            genRow5Cell3(out pc, out pdfTabtmp);
            pc = new PdfPCell(pdfTabtmp);
            pdfTab.AddCell(pc);  //寫入第5列第3~4個cell           
            pc = new PdfPCell(new Phrase("", ChFont)); pc.Border = Rectangle.NO_BORDER;  //設為無邊框
            pdfTab.AddCell(pc);
            //-----第六列 
            pc = new PdfPCell(new Phrase("\n主管____________\n\n經辦____________", ChFont));
            pc.Border = Rectangle.NO_BORDER;  //設為無邊框
            pc.HorizontalAlignment = 2;//0:left 1:center 2:right
            pc.Colspan = 2;
            pdfTab.AddCell(pc); //寫入第6列第1~2個cell
            pc = new PdfPCell(new Phrase("說明:\n(1)本單由委託機關填寫一式三份，檢附在委託郵局代存員工薪資總表交受託局，如因資料錯誤致郵局誤入帳戶，應由委託單位自行負責。\n(2)本單局號，帳號請加註檢查號碼，切勿遺漏。\n(3)受託機關名稱及其所設立之劃撥帳戶帳號，請正確填寫，以利受託局還有帳目無法轉存時理撥退手續。\n(4)委託機構使用媒體或非媒體處理，請在右上角作業方式欄勾註。\n(5)媒體磯片在受託局傳輸者，請在右上角「本局磁片傳輸」欄勾註。\n", ChFont));
            pc.Colspan = 2;
            pc.Border = Rectangle.NO_BORDER;  //設為無邊框
            pdfTab.AddCell(pc); //寫入第4列第3~4個cell
            doc.Add(pdfTab); //將資料寫進文件中       

            ////#######Gen BarCode#############        
            //cb = pdfw.DirectContent;
            //pa = new Paragraph();
            //barcode = new Barcode128();
            //barcode.CodeType = Barcode.CODE128_UCC;
            //barcode.Code = Filename;
            //barcode.StartStopText = true;
            //barcodeImage = barcode.CreateImageWithBarcode(cb, null, null);
            //barcodeImage.ScalePercent(110f);
            //barcodeImage.Alignment = Element.ALIGN_TOP;//.ALIGN_BOTTOM;

            //pa.Add(barcodeImage);
            //doc.Add(pa);
            doc.NewPage();   //強制分頁

            //【明細本文】開始 產生表格 塞入資料 -------------------------- END
        }
        doc.Close();
        HttpContext.Current.Response.Clear();
        HttpContext.Current.Response.AddHeader("Content-Disposition", "attachment;filename=" + Filename + ".pdf"); //+".pdf " + DateTime.Now.Month.ToString().PadLeft(2, '0') + DateTime.Now.Day.ToString().PadLeft(2, '0')
        HttpContext.Current.Response.ContentType = "application/octer-stream";
        HttpContext.Current.Response.OutputStream.Write(memory.GetBuffer(), 0, memory.GetBuffer().Length);
        HttpContext.Current.Response.OutputStream.Flush();
        HttpContext.Current.Response.OutputStream.Close();
        HttpContext.Current.Response.Flush();

    }

    private static void genDetailPageHeader(Document doc, Font ChFontHead, Font ChFont, out PdfPTable pdfTab, out PdfPCell pc, out PdfPTable pdfTabtmp)
    {
        pdfTab = new PdfPTable(new float[] { 1, 1, 4, 3 }); // 建立4個欄位表格之相對寬度        
        pdfTab.TotalWidth = 520f; // 表格總寬
        pdfTab.LockedWidth = true;
        pc = new PdfPCell(new Phrase(" ", ChFont));
        pc.Border = Rectangle.NO_BORDER;  //設為無邊框
        pdfTab.AddCell(pc); //寫入第1列第1個cell   
        pc = new PdfPCell(new Phrase(" ", ChFont));
        pc.Border = Rectangle.NO_BORDER;  //設為無邊框
        pdfTab.AddCell(pc); //寫入第1列第2個cell   
        pc = new PdfPCell(new Phrase("郵政(存簿儲金)薪資存款團體戶存款單", ChFontHead));
        pc.Border = Rectangle.NO_BORDER;  //設為無邊框
        pdfTab.AddCell(pc); //寫入第1列第3個cell  
        pc = new PdfPCell(new Phrase("030 連線\n033 非連線", ChFont));
        pc.Border = Rectangle.NO_BORDER;  //設為無邊框
        pdfTab.AddCell(pc);  //寫入第1列第4個cell       
                             //-----第二列 
        pc = new PdfPCell(new Phrase("受託局名\n及局號戳", ChFont));
        pc.Border = Rectangle.NO_BORDER;  //設為無邊框
        pdfTab.AddCell(pc); //寫入第2列第1個cell   
        pc = new PdfPCell(new Phrase(" ", ChFont));
        pdfTab.AddCell(pc); //寫入第2列第2個cell   
        pc = new PdfPCell(new Phrase("劃撥儲金帳號:\n\n存款日期:  年  月  日", ChFont));
        pc.Border = Rectangle.NO_BORDER;  //設為無邊框
        pdfTab.AddCell(pc); //寫入第2列第3個cell  

        genRow2Cell4(out pc, out pdfTabtmp);
        pc = new PdfPCell(pdfTabtmp);
        pdfTab.AddCell(pc);  //寫入第2列第4個cell   
        pc = new PdfPCell(new Phrase("\n", ChFont)); pc.Border = Rectangle.NO_BORDER;  //設為無邊框    
        pc.Colspan = 4; pdfTab.AddCell(pc); //寫入空列            
        doc.Add(pdfTab);
    }

    private static Image genbarCode(string Filename, PdfContentByte cb)
    {
        Barcode128 barcode = new Barcode128();
        barcode.CodeType = Barcode.CODE128_UCC;
        barcode.Code = Filename;
        barcode.StartStopText = true;
        iTextSharp.text.Image barcodeImage = barcode.CreateImageWithBarcode(cb, null, null);
        barcodeImage.ScalePercent(110f);
        barcodeImage.Alignment = Element.ALIGN_RIGHT;
        return barcodeImage;
    }

    private static void genRow1Cell3(Font ChFont, out PdfPCell pc, out PdfPTable pdfTabtmp) //產生第1列第3個cell中的表格(table)
    {
        pdfTabtmp = new PdfPTable(new float[] { 2, 8, 2 });
        pdfTabtmp.DefaultCell.FixedHeight = 5f;  //高度
        pdfTabtmp.TotalWidth = 30f; // 表格總寬
        //-----Row1Cell3-table-row1
        pc = new PdfPCell(new Phrase("勾註", ChFont));
        pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("資料別及作業方式", ChFont));
        pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("批次", ChFont));
        pdfTabtmp.AddCell(pc);
        //-----Row1Cell3-table-row2
        pc = new PdfPCell(new Phrase(" ", ChFont));
        pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("媒體薪資類(Y)", ChFont));
        pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase(" ", ChFont));
        pdfTabtmp.AddCell(pc);
        //-----Row1Cell3-table-row3
        pc = new PdfPCell(new Phrase(" ", ChFont));
        pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("媒體非薪資類(N)", ChFont));
        pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase(" ", ChFont));
        pdfTabtmp.AddCell(pc);
        //-----Row1Cell3-table-row4
        pc = new PdfPCell(new Phrase(" ", ChFont));
        pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("非媒體類(C)", ChFont));
        pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase(" ", ChFont));
        pdfTabtmp.AddCell(pc);
        //-----Row1Cell3-table-row5
        pc = new PdfPCell(new Phrase(" ", ChFont));
        pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("薪資存款直接傳輸作業", ChFont));
        pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase(" ", ChFont));
        pdfTabtmp.AddCell(pc);
        //-----Row1Cell3-table-row6
        pc = new PdfPCell(new Phrase(" ", ChFont));
        pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("本局磁片傳輸", ChFont));
        pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase(" ", ChFont));
        pdfTabtmp.AddCell(pc);
        //-----Row1Cell3-table-row7
        pc = new PdfPCell(new Phrase(" ", ChFont));
        pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("本局傳輸(非媒體)", ChFont));
        pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase(" ", ChFont));
        pdfTabtmp.AddCell(pc);
    }
    private static void genRow2(Font ChFont, out PdfPCell pc, out PdfPTable pdfTabtmp, int totalCost, int totalAmount, string memo) //產生第2列中的表格(table)
    {
        pdfTabtmp = new PdfPTable(new float[] { 2, 2, 2, 2, 2, 1 });
        pdfTabtmp.DefaultCell.FixedHeight = 5f;
        pdfTabtmp.TotalWidth = 500f; // 表格總寬
        //-----Row1
        pc = new PdfPCell(new Phrase("指定轉存日期", ChFont)); pc.HorizontalAlignment = 1;//0:left 1:center 2:right
        pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("委存總件數", ChFont)); pc.HorizontalAlignment = 1;//0:left 1:center 2:right
        pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("委存總金額", ChFont)); pc.HorizontalAlignment = 1;//0:left 1:center 2:right
        pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("款項細目代碼", ChFont)); pc.HorizontalAlignment = 1;//0:left 1:center 2:right
        pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("款項來源代號\n(本欄由郵局填寫)", ChFont)); pc.HorizontalAlignment = 1;//0:left 1:center 2:right
        pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("備註", ChFont)); pc.HorizontalAlignment = 1;//0:left 1:center 2:right
        pdfTabtmp.AddCell(pc);
        //-----Row2
        pc = new PdfPCell(new Phrase(" ", ChFont));
        pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase(string.Format("{0:N0}", Convert.ToInt32(totalAmount.ToString())), ChFont)); pc.HorizontalAlignment = 1;//0:left 1:center 2:right
        pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase(string.Format("{0:N0}", Convert.ToInt32(totalCost.ToString())), ChFont)); pc.HorizontalAlignment = 1;//0:left 1:center 2:right
        pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase(" ", ChFont));
        pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase(" ", ChFont));
        pdfTabtmp.AddCell(pc);
        pdfTabtmp.DefaultCell.FixedHeight = 30f;
        pc = new PdfPCell(new Phrase(memo, ChFont));
        pdfTabtmp.AddCell(pc);
    }
    private static void genRow2Cell4(out PdfPCell pc, out PdfPTable pdfTabtmp) //產生第2列第4個cell中的表格(table)
    {
        BaseFont bf = BaseFont.CreateFont(@"C:\Windows\Fonts\kaiu.ttf", BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);
        Font ChFont = new Font(bf, 9, Font.NORMAL);
        pdfTabtmp = new PdfPTable(new float[] { 4, 1, 1, 4, 1 });
        //pdfTabtmp.DefaultCell.FixedHeight = 2f;  //高度
        pdfTabtmp.TotalWidth = 50f; // 表格總寬
        //-----Row2Cell3-table-row1
        pc = new PdfPCell(new Phrase("媒體", ChFont));
        pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("", ChFont));
        pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("非媒體", ChFont)); pc.Rowspan = 4;
        pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("資料中心代登", ChFont));
        pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("", ChFont));
        pdfTabtmp.AddCell(pc);
        //-----Row2Cell3-table-row2
        pc = new PdfPCell(new Phrase("本局磁片傳輸", ChFont));
        pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("", ChFont));
        pdfTabtmp.AddCell(pc);

        pc = new PdfPCell(new Phrase("1512入機", ChFont));
        pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("", ChFont));
        pdfTabtmp.AddCell(pc);
        //-----Row2Cell3-table-row3
        pc = new PdfPCell(new Phrase("", ChFont));
        pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("", ChFont));
        pdfTabtmp.AddCell(pc);

        pc = new PdfPCell(new Phrase("後線PC預登", ChFont));
        pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("", ChFont));
        pdfTabtmp.AddCell(pc);
    }
    private static void genRow3(out PdfPCell pc, out PdfPTable pdfTabtmp, DataTable dt, int totalCost, int totalAmount, int startR, int endR, bool ifgo) //產生第3列中的表格(table)【郵局-資料明細表】(主頁-資料頁)
    {
        pdfTabtmp = new PdfPTable(new float[] { 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1 }); //35-field
        pdfTabtmp.DefaultCell.FixedHeight = 5f;  //高度
        pdfTabtmp.TotalWidth = 500f; // 表格總寬
        BaseFont bf = BaseFont.CreateFont(@"C:\Windows\Fonts\kaiu.ttf", BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);
        Font ChFont = new Font(bf, 9, Font.NORMAL);
        //-----第3列-Row1
        pc = new PdfPCell(new Phrase("員工立帳局號", ChFont));
        pc.Colspan = 7; pc.Rowspan = 2; pc.VerticalAlignment = Element.ALIGN_MIDDLE; pc.HorizontalAlignment = 1;//0:left 1:center 2:right
        pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("檢號", ChFont));
        pc.Rowspan = 2; pc.HorizontalAlignment = 1; pc.VerticalAlignment = Element.ALIGN_MIDDLE;
        pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("存簿帳號", ChFont));
        pc.Colspan = 7; pc.Rowspan = 2; pc.HorizontalAlignment = 1; pc.VerticalAlignment = Element.ALIGN_MIDDLE;
        pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("檢號", ChFont));
        pc.Rowspan = 2; pc.HorizontalAlignment = 1; pc.VerticalAlignment = Element.ALIGN_MIDDLE;
        pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("戶名", ChFont));
        pc.Rowspan = 2; pc.HorizontalAlignment = 1; pc.VerticalAlignment = Element.ALIGN_MIDDLE;
        pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("身分證統一編號", ChFont));
        pc.Colspan = 10; pc.Rowspan = 2; pc.HorizontalAlignment = 1; pc.VerticalAlignment = Element.ALIGN_MIDDLE;
        pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("存款金額", ChFont));
        pc.Colspan = 8; pc.HorizontalAlignment = 1; pc.VerticalAlignment = Element.ALIGN_MIDDLE;
        pdfTabtmp.AddCell(pc);
        //-----第3列-Row2
        pc = new PdfPCell(new Phrase("千萬", ChFont)); pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("百萬", ChFont)); pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("十萬", ChFont)); pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("萬", ChFont)); pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("千", ChFont)); pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("百", ChFont)); pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("十", ChFont)); pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("元", ChFont)); pdfTabtmp.AddCell(pc);
        //-----第3列-Row3~Row29   【Data Rows】
        char[] cr;
        int remitPrice;
        for (int i = startR; i < endR; i++)
        {
            if (dt.Rows[i]["CollecACC"].ToString().Length != 14)
            {
                HttpContext.Current.Response.Write("<script>alert('你的帳號資料有誤，郵局為14碼，請逐一確認!!');</scrpt>");
                break;
            }
            cr = dt.Rows[i]["CollecACC"].ToString().Replace("-", "").Trim().ToCharArray(); //郵局局帳號
            pc = new PdfPCell(new Phrase(cr[0].ToString(), ChFont)); pdfTabtmp.AddCell(pc);
            pc = new PdfPCell(new Phrase(cr[1].ToString(), ChFont)); pdfTabtmp.AddCell(pc);
            pc = new PdfPCell(new Phrase(cr[2].ToString(), ChFont)); pdfTabtmp.AddCell(pc);
            pc = new PdfPCell(new Phrase(cr[3].ToString(), ChFont)); pdfTabtmp.AddCell(pc);
            pc = new PdfPCell(new Phrase(cr[4].ToString(), ChFont)); pdfTabtmp.AddCell(pc);
            pc = new PdfPCell(new Phrase(cr[5].ToString(), ChFont)); pdfTabtmp.AddCell(pc);
            pc = new PdfPCell(new Phrase("-", ChFont)); pdfTabtmp.AddCell(pc); //檢號
            pc = new PdfPCell(new Phrase(cr[6].ToString(), ChFont)); pdfTabtmp.AddCell(pc);
            pc = new PdfPCell(new Phrase(cr[7].ToString(), ChFont)); pdfTabtmp.AddCell(pc);
            pc = new PdfPCell(new Phrase(cr[8].ToString(), ChFont)); pdfTabtmp.AddCell(pc);
            pc = new PdfPCell(new Phrase(cr[9].ToString(), ChFont)); pdfTabtmp.AddCell(pc);
            pc = new PdfPCell(new Phrase(cr[10].ToString(), ChFont)); pdfTabtmp.AddCell(pc);
            pc = new PdfPCell(new Phrase(cr[11].ToString(), ChFont)); pdfTabtmp.AddCell(pc);
            pc = new PdfPCell(new Phrase(cr[12].ToString(), ChFont)); pdfTabtmp.AddCell(pc);
            pc = new PdfPCell(new Phrase("-", ChFont)); pdfTabtmp.AddCell(pc); //檢號
            pc = new PdfPCell(new Phrase(cr[13].ToString(), ChFont)); pdfTabtmp.AddCell(pc);
            pc = new PdfPCell(new Phrase(dt.Rows[i]["CollecName"].ToString(), ChFont)); pc.HorizontalAlignment = 1; pdfTabtmp.AddCell(pc);
            if (dt.Rows[i]["CollectId"].ToString().Length == 8)
                cr = dt.Rows[i]["CollectId"].ToString().Trim().ToUpper().PadLeft(10, ' ').ToCharArray(); //身分證字號
            else
                cr = dt.Rows[i]["CollectId"].ToString().Trim().ToUpper().ToCharArray(); //身分證字號
            pc = new PdfPCell(new Phrase(cr[0].ToString(), ChFont)); pdfTabtmp.AddCell(pc);
            pc = new PdfPCell(new Phrase(cr[1].ToString(), ChFont)); pdfTabtmp.AddCell(pc);
            pc = new PdfPCell(new Phrase(cr[2].ToString(), ChFont)); pdfTabtmp.AddCell(pc);
            pc = new PdfPCell(new Phrase(cr[3].ToString(), ChFont)); pdfTabtmp.AddCell(pc);
            pc = new PdfPCell(new Phrase(cr[4].ToString(), ChFont)); pdfTabtmp.AddCell(pc);
            pc = new PdfPCell(new Phrase(cr[5].ToString(), ChFont)); pdfTabtmp.AddCell(pc);
            pc = new PdfPCell(new Phrase(cr[6].ToString(), ChFont)); pdfTabtmp.AddCell(pc);
            pc = new PdfPCell(new Phrase(cr[7].ToString(), ChFont)); pdfTabtmp.AddCell(pc);
            pc = new PdfPCell(new Phrase(cr[8].ToString(), ChFont)); pdfTabtmp.AddCell(pc);
            pc = new PdfPCell(new Phrase(cr[9].ToString(), ChFont)); pdfTabtmp.AddCell(pc);
            remitPrice = Convert.ToInt32(dt.Rows[i]["RemitPrice"].ToString());
            cr = remitPrice.ToString().PadLeft(8, '0').ToCharArray(); //帳款金額
            pc = new PdfPCell(new Phrase(cr[0].ToString(), ChFont)); pdfTabtmp.AddCell(pc); //仟萬
            pc = new PdfPCell(new Phrase(cr[1].ToString(), ChFont)); pdfTabtmp.AddCell(pc); //佰萬
            pc = new PdfPCell(new Phrase(cr[2].ToString(), ChFont)); pdfTabtmp.AddCell(pc); //十萬
            pc = new PdfPCell(new Phrase(cr[3].ToString(), ChFont)); pdfTabtmp.AddCell(pc); //萬
            pc = new PdfPCell(new Phrase(cr[4].ToString(), ChFont)); pdfTabtmp.AddCell(pc); //千 
            pc = new PdfPCell(new Phrase(cr[5].ToString(), ChFont)); pdfTabtmp.AddCell(pc); //百
            pc = new PdfPCell(new Phrase(cr[6].ToString(), ChFont)); pdfTabtmp.AddCell(pc); //十
            pc = new PdfPCell(new Phrase(cr[7].ToString(), ChFont)); pdfTabtmp.AddCell(pc); //個位數
        }
        if (endR < 20) //若資料列小於20列時就執行補足列的動作
        {
            for (int i = 0; i < (20 - dt.Rows.Count); i++) //新增共25行資料(含資料列)
            {
                for (int j = 1; j <= 35; j++)
                {
                    pc = new PdfPCell(new Phrase(" ", ChFont)); pdfTabtmp.AddCell(pc);
                }
            }
        }
        if (ifgo)
            genRow4n(out pc, pdfTabtmp, totalCost, totalAmount, ChFont, out cr);
    }

    private static void genRow4n(out PdfPCell pc, PdfPTable pdfTabtmp, int totalCost, int totalAmount, Font ChFont, out char[] cr)
    {
        //pc = new PdfPCell(new Phrase("小計" + totalAmount + "次", ChFont)); pc.HorizontalAlignment = 2; pc.Colspan = 27; pdfTabtmp.AddCell(pc);
        //cr = totalCost.ToString().PadLeft(8, '0').ToCharArray(); //匯款總金額
        //pc = new PdfPCell(new Phrase(cr[0].ToString(), ChFont)); pdfTabtmp.AddCell(pc);
        //pc = new PdfPCell(new Phrase(cr[1].ToString(), ChFont)); pdfTabtmp.AddCell(pc);
        //pc = new PdfPCell(new Phrase(cr[2].ToString(), ChFont)); pdfTabtmp.AddCell(pc);
        //pc = new PdfPCell(new Phrase(cr[3].ToString(), ChFont)); pdfTabtmp.AddCell(pc);
        //pc = new PdfPCell(new Phrase(cr[4].ToString(), ChFont)); pdfTabtmp.AddCell(pc);
        //pc = new PdfPCell(new Phrase(cr[5].ToString(), ChFont)); pdfTabtmp.AddCell(pc);
        //pc = new PdfPCell(new Phrase(cr[6].ToString(), ChFont)); pdfTabtmp.AddCell(pc);
        //pc = new PdfPCell(new Phrase(cr[7].ToString(), ChFont)); pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("", ChFont)); pc.HorizontalAlignment = 2; pc.Colspan = 35; pdfTabtmp.AddCell(pc);


        pc = new PdfPCell(new Phrase("委託總計", ChFont)); pc.Colspan = 5; pc.Rowspan = 2; pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("共存款" + totalAmount + "次                          新臺幣", ChFont)); pc.Colspan = 12; pc.Rowspan = 2; pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("億", ChFont)); pc.Border = Rectangle.NO_BORDER; pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("千萬", ChFont)); pc.Border = Rectangle.NO_BORDER; pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("佰萬", ChFont)); pc.Border = Rectangle.NO_BORDER; pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("十萬", ChFont)); pc.Border = Rectangle.NO_BORDER; pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("萬", ChFont)); pc.Border = Rectangle.NO_BORDER; pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("千", ChFont)); pc.Border = Rectangle.NO_BORDER; pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("百", ChFont)); pc.Border = Rectangle.NO_BORDER; pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("十", ChFont)); pc.Border = Rectangle.NO_BORDER; pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("元", ChFont)); pc.Border = Rectangle.NO_BORDER; pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("", ChFont)); pc.Border = Rectangle.NO_BORDER; pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("", ChFont)); pc.Border = Rectangle.NO_BORDER; pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("", ChFont)); pc.Colspan = 7; pc.Rowspan = 2; pdfTabtmp.AddCell(pc);
        cr = totalCost.ToString().PadLeft(9, '0').ToCharArray(); //匯款總金額
        pc = new PdfPCell(new Phrase(cr[0].ToString(), ChFont)); pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase(cr[1].ToString(), ChFont)); pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase(cr[2].ToString(), ChFont)); pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase(cr[3].ToString(), ChFont)); pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase(cr[4].ToString(), ChFont)); pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase(cr[5].ToString(), ChFont)); pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase(cr[6].ToString(), ChFont)); pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase(cr[7].ToString(), ChFont)); pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase(cr[8].ToString(), ChFont)); pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("", ChFont)); pc.Border = Rectangle.NO_BORDER; pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("", ChFont)); pc.Border = Rectangle.NO_BORDER; pdfTabtmp.AddCell(pc);
    }

    private static void genRow4(out PdfPCell pc, out PdfPTable pdfTabtmp, DataTable dt, int totalCost, int totalAmount) //產生第3列中的表格(table)【郵局-資料明細表】的最後一列
    {
        pdfTabtmp = new PdfPTable(new float[] { 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1 }); //35-field
        pdfTabtmp.DefaultCell.FixedHeight = 5f;  //高度
        pdfTabtmp.TotalWidth = 520f; // 表格總寬
        BaseFont bf = BaseFont.CreateFont(@"C:\Windows\Fonts\kaiu.ttf", BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);
        Font ChFont = new Font(bf, 9, Font.NORMAL);
        char[] cr;

        //pc = new PdfPCell(new Phrase("小計" + totalAmount + "次", ChFont)); pc.HorizontalAlignment = 2; pc.Colspan = 27; pdfTabtmp.AddCell(pc);
        //cr = totalCost.ToString().PadLeft(8, '0').ToCharArray(); //匯款總金額
        //pc = new PdfPCell(new Phrase(cr[0].ToString(), ChFont)); pdfTabtmp.AddCell(pc);
        //pc = new PdfPCell(new Phrase(cr[1].ToString(), ChFont)); pdfTabtmp.AddCell(pc);
        //pc = new PdfPCell(new Phrase(cr[2].ToString(), ChFont)); pdfTabtmp.AddCell(pc);
        //pc = new PdfPCell(new Phrase(cr[3].ToString(), ChFont)); pdfTabtmp.AddCell(pc);
        //pc = new PdfPCell(new Phrase(cr[4].ToString(), ChFont)); pdfTabtmp.AddCell(pc);
        //pc = new PdfPCell(new Phrase(cr[5].ToString(), ChFont)); pdfTabtmp.AddCell(pc);
        //pc = new PdfPCell(new Phrase(cr[6].ToString(), ChFont)); pdfTabtmp.AddCell(pc);
        //pc = new PdfPCell(new Phrase(cr[7].ToString(), ChFont)); pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase(" ", ChFont)); pc.HorizontalAlignment = 2; pc.Colspan = 35; pdfTabtmp.AddCell(pc);

        pc = new PdfPCell(new Phrase("委託總計", ChFont)); pc.Colspan = 5; pc.Rowspan = 2; pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("共存款" + totalAmount + "次                          新臺幣", ChFont)); pc.Colspan = 12; pc.Rowspan = 2; pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("億", ChFont)); pc.Border = Rectangle.NO_BORDER; pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("千萬", ChFont)); pc.Border = Rectangle.NO_BORDER; pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("佰萬", ChFont)); pc.Border = Rectangle.NO_BORDER; pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("十萬", ChFont)); pc.Border = Rectangle.NO_BORDER; pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("萬", ChFont)); pc.Border = Rectangle.NO_BORDER; pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("千", ChFont)); pc.Border = Rectangle.NO_BORDER; pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("百", ChFont)); pc.Border = Rectangle.NO_BORDER; pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("十", ChFont)); pc.Border = Rectangle.NO_BORDER; pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("元", ChFont)); pc.Border = Rectangle.NO_BORDER; pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("", ChFont)); pc.Border = Rectangle.NO_BORDER; pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("", ChFont)); pc.Border = Rectangle.NO_BORDER; pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("", ChFont)); pc.Colspan = 7; pc.Rowspan = 2; pdfTabtmp.AddCell(pc);
        cr = totalCost.ToString().PadLeft(9, '0').ToCharArray(); //匯款總金額
        pc = new PdfPCell(new Phrase(cr[0].ToString(), ChFont)); pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase(cr[1].ToString(), ChFont)); pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase(cr[2].ToString(), ChFont)); pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase(cr[3].ToString(), ChFont)); pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase(cr[4].ToString(), ChFont)); pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase(cr[5].ToString(), ChFont)); pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase(cr[6].ToString(), ChFont)); pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase(cr[7].ToString(), ChFont)); pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase(cr[8].ToString(), ChFont)); pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("", ChFont)); pc.Border = Rectangle.NO_BORDER; pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("", ChFont)); pc.Border = Rectangle.NO_BORDER; pdfTabtmp.AddCell(pc);
    }
    private static void genRow4Cell3(Font ChFont, out PdfPCell pc, out PdfPTable pdfTabtmp) //產生第4列中的表格(table)
    {
        pdfTabtmp = new PdfPTable(new float[] { 1 });
        pdfTabtmp.TotalWidth = 30f; // 表格總寬
        pc = new PdfPCell(new Phrase("\n\n\n\n\n\n\n\n\n", ChFont));
        pdfTabtmp.AddCell(pc);
    }
    private static void genRow7(Font ChFont, out PdfPCell pc, out PdfPTable pdfTabtmp) //產生第7列中的表格(table)
    {
        pdfTabtmp = new PdfPTable(new float[] { 1, 1, 1, 2, 1, 1, 1 });
        pdfTabtmp.DefaultCell.FixedHeight = 5f;  //高度
        pdfTabtmp.TotalWidth = 500f; // 表格總寬
        //-----Row1
        pc = new PdfPCell(new Phrase("直接傳輸作業", ChFont));
        pc.Rowspan = 3;
        pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("1.傳送時間：__月__日__午__時__分", ChFont));
        pc.Rowspan = 3;
        pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("2.是否須回送存款入帳詳情", ChFont));
        pc.Rowspan = 3;
        pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("□Y回送紙本\n□E回送電子檔\n□B回送紙本及電子檔\n□N不回送", ChFont));
        pc.Rowspan = 3;
        pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("實際轉存", ChFont));
        pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("_________筆", ChFont));
        pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("_________元", ChFont));
        pdfTabtmp.AddCell(pc);
        //-----Row2
        pc = new PdfPCell(new Phrase("撥退", ChFont));
        pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("_________筆", ChFont));
        pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("_________元", ChFont));
        pdfTabtmp.AddCell(pc);
        //-----Row3
        pc = new PdfPCell(new Phrase("經辦:__________  主管:__________", ChFont));
        pc.Colspan = 3;
        pdfTabtmp.AddCell(pc);
    }
    //存款團體戶清單start
    public void ExportPostDetailPDF(ref Document doc)   //匯出【郵局-存款團體戶清單】pdf檔
    {
        YuMingClass.DataFunc myfunc = new DataFunc();
        Dictionary<string, object> Dic = new Dictionary<string, object>();
        //var doc = new Document(PageSize.A4, 50, 40, 50, 50); //L R U B
        MemoryStream memory = new MemoryStream();
        //PdfWriter pdfw = PdfWriter.GetInstance(doc, memory);
        BaseFont bf = BaseFont.CreateFont(@"C:\Windows\Fonts\kaiu.ttf", BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);
        Font ChFontHead = new Font(bf, 12, Font.BOLD);
        Font ChFont = new Font(bf, 11, Font.NORMAL);
        //Font ChFontBold = new Font(bf, 12, Font.BOLD);
        // Dic["@Sn"] = sn;
        // doc.Open();
        //本文開始 產生表格 -- START     
        //-----第一列  
        PdfPTable pdfTab = new PdfPTable(new float[] { 1, 1, 4, 3 }); // 建立4個欄位表格之相對寬度        
        pdfTab.TotalWidth = 520f; // 表格總寬
        pdfTab.LockedWidth = true;
        PdfPCell pc = new PdfPCell(new Phrase(" ", ChFont));
        pc.Border = Rectangle.NO_BORDER;  //設為無邊框
        pdfTab.AddCell(pc); //寫入第1列第1個cell   
        pc = new PdfPCell(new Phrase(" ", ChFont));
        pc.Border = Rectangle.NO_BORDER;  //設為無邊框
        pdfTab.AddCell(pc); //寫入第1列第2個cell   
        pc = new PdfPCell(new Phrase("郵政(存簿儲金)薪資存款團體戶存款單", ChFontHead));
        pc.Border = Rectangle.NO_BORDER;  //設為無邊框
        pdfTab.AddCell(pc); //寫入第1列第3個cell  
        pc = new PdfPCell(new Phrase("030 連線\n033 非連線", ChFont));
        pc.Border = Rectangle.NO_BORDER;  //設為無邊框
        pdfTab.AddCell(pc);  //寫入第1列第4個cell       
        //-----第二列 
        pc = new PdfPCell(new Phrase("受託局名\n及局號戳", ChFont));
        pc.Border = Rectangle.NO_BORDER;  //設為無邊框
        pdfTab.AddCell(pc); //寫入第2列第1個cell   
        pc = new PdfPCell(new Phrase(" ", ChFont));
        pdfTab.AddCell(pc); //寫入第2列第2個cell   
        pc = new PdfPCell(new Phrase("劃撥儲金帳號:\n\n存款日期:  年  月  日", ChFont));
        pc.Border = Rectangle.NO_BORDER;  //設為無邊框
        pdfTab.AddCell(pc); //寫入第2列第3個cell  
        PdfPTable pdfTabtmp;
        genRow2Cell4(out pc, out pdfTabtmp);
        pc = new PdfPCell(pdfTabtmp);
        pdfTab.AddCell(pc);  //寫入第2列第4個cell   
        pc = new PdfPCell(new Phrase("\n", ChFont)); pc.Border = Rectangle.NO_BORDER;  //設為無邊框    
        pc.Colspan = 4; pdfTab.AddCell(pc); //寫入空列
                                            //-----第三列
                                            // genRow3(out pc, out pdfTabtmp, dt);
        pc = new PdfPCell(pdfTabtmp);
        pc.Colspan = 4;
        pdfTab.AddCell(pc); //寫入第2列第1個cell       
        //-----第四列 
        pc = new PdfPCell(new Phrase("\n委託機構名稱:宜蘭縣政府   主管:____________    經辦:_____________  \n\n委託機構地址:260宜蘭市縣政北路1號       連絡電話:___________\n", ChFont));
        pc.Colspan = 4;
        pc.Border = Rectangle.NO_BORDER;  //設為無邊框
        pdfTab.AddCell(pc); //寫入第4列   
        //-----第五列 
        pc = new PdfPCell(new Phrase("\n(非連線)\n立帳局郵戳", ChFont));
        pc.Border = Rectangle.NO_BORDER;  //設為無邊框
        pc.HorizontalAlignment = 2;//0:left 1:center 2:right
        pc.Colspan = 2;
        pdfTab.AddCell(pc); //寫入第5列第1~2個cell

        genRow5Cell3(out pc, out pdfTabtmp);
        pc = new PdfPCell(pdfTabtmp);
        pdfTab.AddCell(pc);  //寫入第5列第3~4個cell           
        pc = new PdfPCell(new Phrase("", ChFont)); pc.Border = Rectangle.NO_BORDER;  //設為無邊框
        pdfTab.AddCell(pc);
        //-----第六列 
        pc = new PdfPCell(new Phrase("\n主管____________\n\n經辦____________", ChFont));
        pc.Border = Rectangle.NO_BORDER;  //設為無邊框
        pc.HorizontalAlignment = 2;//0:left 1:center 2:right
        pc.Colspan = 2;
        pdfTab.AddCell(pc); //寫入第6列第1~2個cell
        pc = new PdfPCell(new Phrase("說明:\n(1)本單由委託機關填寫一式二份，檢附在委託郵局代存員工薪資總表交受託局，如因資料錯誤致郵局誤入帳戶，應由委託單位自行負責。\n(2)本單局號，帳號請加註檢查號碼，切勿遺漏。\n(3)受託機關名稱及其所設立之劃撥帳戶帳號，請正確填寫，以利受託局還有帳目無法轉存時理撥退手續。\n(4)委託機構使用媒體或非媒體處理，請在右上角作業方式欄勾註。\n(5)媒體磯片在受託局傳輸者，請在右上角「本局磁片傳輸」欄勾註。", ChFont));
        pc.Colspan = 2;
        pc.Border = Rectangle.NO_BORDER;  //設為無邊框
        pdfTab.AddCell(pc); //寫入第4列第3~4個cell

        doc.Add(pdfTab); //將資料寫進文件中          
        // 塞入資料 -------------------------- END
        doc.Close();
        HttpContext.Current.Response.Clear();
        HttpContext.Current.Response.AddHeader("Content-Disposition", "attachment;filename=YilanExportPDF_" + DateTime.Now.Month.ToString().PadLeft(2, '0') + DateTime.Now.Day.ToString().PadLeft(2, '0') + ".pdf");
        HttpContext.Current.Response.ContentType = "application/octer-stream";
        HttpContext.Current.Response.OutputStream.Write(memory.GetBuffer(), 0, memory.GetBuffer().Length);
        HttpContext.Current.Response.OutputStream.Flush();
        HttpContext.Current.Response.OutputStream.Close();
        HttpContext.Current.Response.Flush();
    }
    private static void genRow5Cell3(out PdfPCell pc, out PdfPTable pdfTabtmp) //產生第2列第4個cell中的表格(table)
    {
        BaseFont bf = BaseFont.CreateFont(@"C:\Windows\Fonts\kaiu.ttf", BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);
        Font ChFont = new Font(bf, 9, Font.NORMAL);
        pdfTabtmp = new PdfPTable(new float[] { 7, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1 });
        //-----Row5Cell3-table-row1
        pc = new PdfPCell(new Phrase("項目", ChFont)); pc.Rowspan = 2; pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("次數", ChFont)); pc.Rowspan = 2; pc.Colspan = 6; pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("金額", ChFont)); pc.Colspan = 11; pc.HorizontalAlignment = 1; pdfTabtmp.AddCell(pc);
        //-----Row5Cell3-table-row2
        pc = new PdfPCell(new Phrase("億", ChFont)); pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("千萬", ChFont)); pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("佰萬", ChFont)); pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("十萬", ChFont)); pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("萬", ChFont)); pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("千", ChFont)); pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("百", ChFont)); pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("十", ChFont)); pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("元", ChFont)); pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("角", ChFont)); pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("分", ChFont)); pdfTabtmp.AddCell(pc);
        //-----Row5Cell3-table-row3
        pc = new PdfPCell(new Phrase("實存存簿儲金", ChFont)); pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("", ChFont)); pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("", ChFont)); pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("", ChFont)); pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("", ChFont)); pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("", ChFont)); pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("", ChFont)); pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("", ChFont)); pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("", ChFont)); pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("", ChFont)); pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("", ChFont)); pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("", ChFont)); pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("", ChFont)); pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("", ChFont)); pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("", ChFont)); pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("", ChFont)); pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("", ChFont)); pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("", ChFont)); pdfTabtmp.AddCell(pc);
        //-----Row5Cell3-table-row4
        pc = new PdfPCell(new Phrase("送存劃撥儲金", ChFont)); pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("", ChFont)); pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("", ChFont)); pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("", ChFont)); pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("", ChFont)); pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("", ChFont)); pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("", ChFont)); pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("", ChFont)); pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("", ChFont)); pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("", ChFont)); pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("", ChFont)); pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("", ChFont)); pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("", ChFont)); pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("", ChFont)); pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("", ChFont)); pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("", ChFont)); pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("", ChFont)); pdfTabtmp.AddCell(pc);
        pc = new PdfPCell(new Phrase("", ChFont)); pdfTabtmp.AddCell(pc);

    }
    //######################郵局報表區End#####################################
    //######################台銀報表區Start###################################
    public void ExportTWBankPDF(string sn)   //匯出【台銀-存款戶清單】pdf檔
    {
        YuMingClass.DataFunc myfunc = new DataFunc();
        Dictionary<string, object> Dic = new Dictionary<string, object>();
        var doc = new Document(PageSize.A4, 50, 50, 60, 50); //L R U B
        MemoryStream memory = new MemoryStream();
        PdfWriter pdfw = PdfWriter.GetInstance(doc, memory);
        BaseFont bf = BaseFont.CreateFont(@"C:\Windows\Fonts\kaiu.ttf", BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);
        Font ChFont = new Font(bf, 10, Font.NORMAL);
        PdfPTable pdfTab;

        int totalAmount = 0; //總筆數
        int totalCost = 0; //總金額
        int totalFee = 0; //手續費總額
        string ConOrga = ""; //彙整單位

        int pagePerCount = 16; //每頁筆數(預設20筆)
        int pageCount = 1; //總頁數
        int dataFrom = 0; //資料起始位置
        int dataEnd = 15; //資料結束位置

        Dic["@Sn"] = sn;

        string FileName = "CR" + sn.PadLeft(6, '0') + "_TWBANK";
        doc.Open();

        for (int k = 0; k < 3; k++) //一式三份
        {
            totalFee = 0;
            if (k > 0)
                doc.NewPage(); //強制換頁
            DataTable dt = myfunc.DBGetDataTable("ComRemitConnectionString", "select * from RemitedList where ConSno=@sn ORDER BY sno", Dic);
            DateTime cashDate;
            PdfPCell pc;

            PdfContentByte cb = pdfw.DirectContent;
            Paragraph pa = new Paragraph();
            Barcode128 barcode = new Barcode128();
            iTextSharp.text.Image barcodeImage;
            while (totalAmount < dt.Rows.Count)
            {
                if (totalAmount == 0)
                {
                    Dic["@organum"] = dt.Rows[0]["ConUnit"].ToString().Substring(0, 3);
                    ConOrga = myfunc.DBGetOneRec("personConnectionString", "select orname from orgat where ornum=@organum", Dic);
                }
                totalCost = totalCost + Convert.ToInt32(dt.Rows[totalAmount]["RemitPrice"]);
                try
                {
                    cashDate = Convert.ToDateTime(dt.Rows[totalAmount]["CashDate"]);
                }
                catch (Exception)
                {
                }
                totalAmount++;
            }
            pageCount = (int)Math.Ceiling(Convert.ToDouble(totalAmount) / pagePerCount); //總頁數
                                                                                         //本文開始 產生表格 -- START               

            for (int page = 1; page <= pageCount; page++) //每頁10筆資料，超過就另印一頁
            {
                pdfTab = new PdfPTable(new float[] { 2, 2, 2 }); // 建立5個欄位表格之相對寬度
                pdfTab.TotalWidth = 520f; // 表格總寬            
                pdfTab.LockedWidth = true;
                // 塞入資料 -------------------------- START         
                //-----第一列
                pc = new PdfPCell(new Phrase("整 批 匯 款 資 料 清 單", ChFont));
                pc.Border = Rectangle.NO_BORDER;  //設為無邊框 
                pdfTab.AddCell(pc); //寫入第1列第1個cell   
                pc = new PdfPCell(new Phrase(" ", ChFont)); pc.Border = Rectangle.NO_BORDER;  //設為無邊框 
                pdfTab.AddCell(pc); //寫入第1列第2個cell  
                pc = new PdfPCell(new Phrase("頁次：" + page + "/" + pageCount, ChFont)); pc.Border = Rectangle.NO_BORDER;  //設為無邊框 
                pdfTab.AddCell(pc); //寫入第1列第3個cell  
                                    //-----第二列
                pc = new PdfPCell(new Phrase("匯款人:宜府" + ConOrga, ChFont)); pc.Border = Rectangle.NO_BORDER;  //設為無邊框 
                pdfTab.AddCell(pc); //寫入第2列第1個cell 
                pc = new PdfPCell(new Phrase(" ", ChFont)); pc.Border = Rectangle.NO_BORDER;  //設為無邊框 
                pdfTab.AddCell(pc); //寫入第2列第2個cell  
                pc = new PdfPCell(new Phrase("製表日：" + (DateTime.Now.Year - 1911) + "年" + DateTime.Now.Month + "月" + DateTime.Now.Day + "日", ChFont)); pc.Border = Rectangle.NO_BORDER;  //設為無邊框 
                pdfTab.AddCell(pc); //寫入第2列第3個cell   
                                    //-----第三列
                pc = new PdfPCell(new Phrase("匯款行:0040222[台灣銀行宜蘭分行]", ChFont)); pc.Border = Rectangle.NO_BORDER;  //設為無邊框 
                pdfTab.AddCell(pc); //寫入第3列第1個cell 
                pc = new PdfPCell(new Phrase(" ", ChFont)); pc.Border = Rectangle.NO_BORDER;  //設為無邊框 
                pdfTab.AddCell(pc); //寫入第3列第2個cell  
                string ConMemo = getConMemo(sn); //批號(BatchNumber)

                pc = new PdfPCell(new Phrase("用途：" + ConMemo, ChFont)); pc.Border = Rectangle.NO_BORDER;  //設為無邊框 
                pdfTab.AddCell(pc); //寫入第3列第3個cell  
                                    //-----第四列
                pc = new PdfPCell(new Phrase("匯款日: 年 月 日", ChFont)); pc.Border = Rectangle.NO_BORDER;  //設為無邊框 
                pdfTab.AddCell(pc); //寫入第4列第1個cell 
                pc = new PdfPCell(new Phrase(" ", ChFont)); pc.Border = Rectangle.NO_BORDER;  //設為無邊框 
                pdfTab.AddCell(pc); //寫入第4列第2個cell
                string No = getBatchNum(sn); //批號(BatchNumber)

                pc = new PdfPCell(new Phrase("批號：" + No, ChFont)); pc.Border = Rectangle.NO_BORDER;  //設為無邊框 
                pdfTab.AddCell(pc); //寫入第4列第3個cell  
                                    //-----第五列************************************************************************************資料列!!!****************
                pc = new PdfPCell(new Phrase("", ChFont)); pc.Border = Rectangle.NO_BORDER;  //設為無邊框 
                pdfTab.AddCell(pc); //寫入第5列第1個cell 
                pc = new PdfPCell(new Phrase(" ", ChFont)); pc.Border = Rectangle.NO_BORDER;  //設為無邊框 
                pdfTab.AddCell(pc); //寫入第5列第2個cell  
                pc = new PdfPCell(new Phrase("檔名：" + FileName, ChFont)); pc.Border = Rectangle.NO_BORDER;  //設為無邊框 
                pdfTab.AddCell(pc); //寫入第5列第3個cell
                doc.Add(pdfTab);
                genDataRowHdr(out pc, out pdfTab);
                doc.Add(pdfTab); //將資料寫進表頭

                dataFrom = (page - 1) * pagePerCount;
                if (page == pageCount)
                    dataEnd = totalAmount;
                else
                    dataEnd = (page) * pagePerCount;

                GenDataRowData(out pc, out pdfTab, ref totalFee, dt, dataFrom, dataEnd);
                doc.Add(pdfTab); //將資料寫進文件中【資料明細】  
                                 // 塞入資料 -------------------------- END

                if (page < pageCount)  //第1頁至最後1頁之前(最後1頁以外)
                {
                    //#######Gen BarCode#############        
                    cb = pdfw.DirectContent;
                    pa = new Paragraph();
                    barcode = new Barcode128();
                    barcode.CodeType = Barcode.CODE128_UCC;
                    barcode.Code = FileName; //【CR】代表為【作業登打系統】
                    barcode.StartStopText = true;
                    barcodeImage = barcode.CreateImageWithBarcode(cb, null, null);
                    barcodeImage.ScalePercent(110f);
                    barcodeImage.Alignment = Element.ALIGN_BOTTOM;

                    pa.Add(barcodeImage);
                    doc.Add(pa);
                    doc.NewPage(); //強制換頁
                }
            }


            //=========表底======================
            string conPer = ""; //製表人姓名
            string conPerID = ""; //製表人身份證字號
            string ConPerTel = ""; //製表人電話
            pdfTab = new PdfPTable(new float[] { 2, 2, 3, 3, 2, 3, 6 }); //7-field
            pdfTab.DefaultCell.FixedHeight = 5f;  //高度
            pdfTab.TotalWidth = 520f; // 表格總寬     
            pdfTab.LockedWidth = true;
            if (dt.Rows.Count > 0)
            {
                conPer = getConInf(dt.Rows[0]["ConPer"].ToString())[0]; //製表人姓名
                conPerID = getConInf(dt.Rows[0]["ConPer"].ToString())[1]; //製表人身份證字號
                ConPerTel = getConInf(dt.Rows[0]["ConPer"].ToString())[2]; //製表人電話
            }

            pc = new PdfPCell(new Phrase("資料總筆數:" + totalAmount + "       資料總金額:", ChFont)); pc.Border = Rectangle.NO_BORDER;
            pc.HorizontalAlignment = 2;//0:left 1:center 2:right
            pc.Colspan = 3; pdfTab.AddCell(pc); //cell-1~3

            pc = new PdfPCell(new Phrase(string.Format("{0:N0}", Convert.ToInt32((totalCost - totalFee).ToString())), ChFont)); pc.Border = Rectangle.NO_BORDER; pc.HorizontalAlignment = 2;//0:left 1:center 2:right
            pdfTab.AddCell(pc); //cell-4 帳款金額

            pc = new PdfPCell(new Phrase(totalFee.ToString(), ChFont)); pc.Border = Rectangle.NO_BORDER; pc.HorizontalAlignment = 2;//0:left 1:center 2:right
            pdfTab.AddCell(pc); //cell-5 手續費總額

            pc = new PdfPCell(new Phrase(string.Format("{0:N0}", Convert.ToInt32(totalCost.ToString())), ChFont)); pc.Border = Rectangle.NO_BORDER; pc.HorizontalAlignment = 2;//0:left 1:center 2:right 
            pdfTab.AddCell(pc); //cell-6  合計 
            pc = new PdfPCell(new Phrase(" ", ChFont)); pc.Border = Rectangle.NO_BORDER; pc.HorizontalAlignment = 1;//0:left 1:center 2:right
            pdfTab.AddCell(pc); //cell-7

            pc = new PdfPCell(new Phrase("\n", ChFont)); pc.Border = Rectangle.NO_BORDER;
            pc.Colspan = 7; pdfTab.AddCell(pc); //空白列

            pc = new PdfPCell(new Phrase("經辦:", ChFont)); pc.Border = Rectangle.NO_BORDER;
            pc.HorizontalAlignment = 0;//0:left 1:center 2:right
            pc.Colspan = 4; pdfTab.AddCell(pc); //cell-1~4
            pc = new PdfPCell(new Phrase("單位主管:", ChFont)); pc.Border = Rectangle.NO_BORDER;
            pc.HorizontalAlignment = 0;//0:left 1:center 2:right
            pc.Colspan = 3; pdfTab.AddCell(pc); //cell-5~7

            pc = new PdfPCell(new Phrase("\n", ChFont)); pc.Border = Rectangle.NO_BORDER;
            pc.Colspan = 7; pdfTab.AddCell(pc); //空白列

            pc = new PdfPCell(new Phrase("製表人:" + conPer, ChFont)); pc.Border = Rectangle.NO_BORDER;
            pc.HorizontalAlignment = 0;//0:left 1:center 2:right
            pc.Colspan = 4; pdfTab.AddCell(pc); //cell-1~4
            pc = new PdfPCell(new Phrase("電話:" + ConPerTel, ChFont)); pc.Border = Rectangle.NO_BORDER;
            pc.HorizontalAlignment = 0;//0:left 1:center 2:right
            pc.Colspan = 3; pdfTab.AddCell(pc); //cell-5~7

            pc = new PdfPCell(new Phrase("\n", ChFont)); pc.Border = Rectangle.NO_BORDER;
            pc.Colspan = 7; pdfTab.AddCell(pc); //空白列

            pc = new PdfPCell(new Phrase("身分證字號:" + conPerID, ChFont)); pc.Border = Rectangle.NO_BORDER;
            pc.HorizontalAlignment = 0;//0:left 1:center 2:right
            pc.Colspan = 4; pdfTab.AddCell(pc); //cell-1~4
            pc = new PdfPCell(new Phrase("電子檔傳送日期:", ChFont)); pc.Border = Rectangle.NO_BORDER;
            pc.HorizontalAlignment = 0;//0:left 1:center 2:right
            pc.Colspan = 3; pdfTab.AddCell(pc); //cell-5~7

            doc.Add(pdfTab);

            //#######Gen BarCode#############        
            cb = pdfw.DirectContent;
            pa = new Paragraph();
            barcode = new Barcode128();
            barcode.CodeType = Barcode.CODE128_UCC;
            barcode.Code = FileName; //【CR】代表為【作業登打系統】
            barcode.StartStopText = true;
            barcodeImage = barcode.CreateImageWithBarcode(cb, null, null);
            barcodeImage.ScalePercent(110f);
            barcodeImage.Alignment = Element.ALIGN_BOTTOM;

            pa.Add(barcodeImage);
            doc.Add(pa);

        }

        doc.Close();
        HttpContext.Current.Response.Clear();
        HttpContext.Current.Response.AddHeader("Content-Disposition", "attachment;filename=" + FileName + ".pdf");
        HttpContext.Current.Response.ContentType = "application/octer-stream";
        HttpContext.Current.Response.OutputStream.Write(memory.GetBuffer(), 0, memory.GetBuffer().Length);
        HttpContext.Current.Response.OutputStream.Flush();
        HttpContext.Current.Response.OutputStream.Close();
        HttpContext.Current.Response.Flush();
    }
    private static void genDataRowHdr(out PdfPCell pc, out PdfPTable pdfTabtmp) //產生第5列中的表頭(table)
    {
        pdfTabtmp = new PdfPTable(new float[] { 2, 2, 3, 3, 2, 3, 6 }); //7-field
        pdfTabtmp.DefaultCell.FixedHeight = 5f;  //高度
        pdfTabtmp.TotalWidth = 520f; // 表格總寬     
        pdfTabtmp.LockedWidth = true;

        BaseFont bf = BaseFont.CreateFont(@"C:\Windows\Fonts\kaiu.ttf", BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);
        Font ChFont = new Font(bf, 11, Font.NORMAL);
        //-----第Header列
        pc = new PdfPCell(new Phrase("序 號", ChFont)); pc.Border = Rectangle.NO_BORDER; pc.HorizontalAlignment = 1;//0:left 1:center 2:right
        pdfTabtmp.AddCell(pc); //cell-1
        pc = new PdfPCell(new Phrase("解款行", ChFont)); pc.Border = Rectangle.NO_BORDER; pc.HorizontalAlignment = 1;//0:left 1:center 2:right
        pdfTabtmp.AddCell(pc); //cell-2
        pc = new PdfPCell(new Phrase("帳 號", ChFont)); pc.Border = Rectangle.NO_BORDER; pc.HorizontalAlignment = 1;//0:left 1:center 2:right
        pdfTabtmp.AddCell(pc); //cell-3
        pc = new PdfPCell(new Phrase("匯款金額", ChFont)); pc.Border = Rectangle.NO_BORDER; pc.HorizontalAlignment = 1;//0:left 1:center 2:right
        pdfTabtmp.AddCell(pc); //cell-4
        pc = new PdfPCell(new Phrase("手續費", ChFont)); pc.Border = Rectangle.NO_BORDER; pc.HorizontalAlignment = 1;//0:left 1:center 2:right
        pdfTabtmp.AddCell(pc); //cell-5
        pc = new PdfPCell(new Phrase("合   計\n(帳款金額)", ChFont)); pc.Border = Rectangle.NO_BORDER; pc.HorizontalAlignment = 1;//0:left 1:center 2:right
        pdfTabtmp.AddCell(pc); //cell-6
        pc = new PdfPCell(new Phrase("收款人姓名\n備      註", ChFont)); pc.Border = Rectangle.NO_BORDER; pc.HorizontalAlignment = 1;//0:left 1:center 2:right
        pdfTabtmp.AddCell(pc); //cell-7
        pc = new PdfPCell(new Phrase("-------------------------------------------------------------------------------------", ChFont)); pc.Border = Rectangle.NO_BORDER; pc.HorizontalAlignment = 1;//0:left 1:center 2:right
        pc.Colspan = 7; pdfTabtmp.AddCell(pc); //cell-7

    }
    private static void GenDataRowData(out PdfPCell pc, out PdfPTable pdfTabtmp, ref int totalFee, DataTable dt, int datafrom, int dataend) //產生第6列中的表格(table)
    {
        pdfTabtmp = new PdfPTable(new float[] { 2, 2, 3, 3, 2, 3, 6 }); //7-field
        pdfTabtmp.DefaultCell.FixedHeight = 5f;  //高度
        pdfTabtmp.TotalWidth = 520f; // 表格總寬     
        pdfTabtmp.LockedWidth = true;
        int remitPrice;
        int fee; //手續費
        string memo = ""; //備註


        BaseFont bf = BaseFont.CreateFont(@"C:\Windows\Fonts\kaiu.ttf", BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);
        Font ChFont = new Font(bf, 10, Font.NORMAL);

        //-----Data列 
        for (int i = datafrom; i < dataend; i++)
        {
            remitPrice = Convert.ToInt32(dt.Rows[i]["RemitPrice"].ToString());
            if (dt.Rows[i]["IfFee"].ToString().Trim().Equals("是"))
            {
                fee = 30;
                totalFee += fee;
                remitPrice = remitPrice - fee;
            }
            else
                fee = 0;
            pc = new PdfPCell(new Phrase((i + 1).ToString(), ChFont)); pc.Border = Rectangle.NO_BORDER; pc.HorizontalAlignment = 1;//0:left 1:center 2:right
            pdfTabtmp.AddCell(pc); //cell-1

            pc = new PdfPCell(new Phrase(dt.Rows[i]["CollectNo"].ToString(), ChFont)); pc.Border = Rectangle.NO_BORDER; pc.HorizontalAlignment = 1;//0:left 1:center 2:right
            pdfTabtmp.AddCell(pc); //cell-2

            pc = new PdfPCell(new Phrase(dt.Rows[i]["CollecAcc"].ToString().Replace("-", ""), ChFont)); pc.Border = Rectangle.NO_BORDER; pc.HorizontalAlignment = 0;//0:left 1:center 2:right
            pdfTabtmp.AddCell(pc); //cell-3

            pc = new PdfPCell(new Phrase(string.Format("{0:N0}", Convert.ToInt32(remitPrice.ToString())), ChFont)); pc.Border = Rectangle.NO_BORDER; ; pc.HorizontalAlignment = 2;//0:left 1:center 
            pdfTabtmp.AddCell(pc); //cell-4 (帳款金額)

            pc = new PdfPCell(new Phrase(fee.ToString(), ChFont)); pc.Border = Rectangle.NO_BORDER; pc.HorizontalAlignment = 2;//0:left 1:center 2:right
            pdfTabtmp.AddCell(pc); //cell-5 (手續費)

            pc = new PdfPCell(new Phrase(string.Format("{0:N0}", Convert.ToInt32((remitPrice + fee).ToString())), ChFont)); pc.Border = Rectangle.NO_BORDER; pc.HorizontalAlignment = 2;//0:left 1:center 2:right
            pdfTabtmp.AddCell(pc); //cell-6 (匯款總金額)

            memo = dt.Rows[i]["RemitMemo"].ToString(); //備註
            if (memo.Length > 12) //ori: 27 <2019/11/8>  problem:73822
                memo = memo.Substring(0, 12) + "..";
            pc = new PdfPCell(new Phrase("(" + dt.Rows[i]["CollecName"].ToString() + ")" + memo, ChFont)); pc.Border = Rectangle.NO_BORDER;
            pdfTabtmp.AddCell(pc); //cell-7
        }
        pc = new PdfPCell(new Phrase("-------------------------------------------------------------------------------------", ChFont)); pc.Border = Rectangle.NO_BORDER;
        pc.Colspan = 7; pdfTabtmp.AddCell(pc);

    }
    //######################台銀報表區End#######################################
    //######################台銀舊報表區Start###################################
    public void ExportTWBankOldPDF(string sn)   //匯出【台銀-存款戶清單】pdf檔
    {
        YuMingClass.DataFunc myfunc = new DataFunc();
        Dictionary<string, object> Dic = new Dictionary<string, object>();

        var doc = new Document(PageSize.A4, 60, 50, 60, 50); //L R U B
        MemoryStream memory = new MemoryStream();
        PdfWriter pdfw = PdfWriter.GetInstance(doc, memory);
        BaseFont bf = BaseFont.CreateFont(@"C:\Windows\Fonts\kaiu.ttf", BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);
        Font ChFontHead = new Font(bf, 20, Font.BOLD);
        Font ChFont = new Font(bf, 14, Font.NORMAL);
        Dic["@Sn"] = sn;
        doc.Open();
        //標頭
        Chunk c = new Chunk("委託台灣銀行代存員工　存款團體戶存款清單\n\n", ChFontHead);
        Phrase p0 = new Phrase(c);
        Paragraph pg0 = new Paragraph(p0);
        pg0.Alignment = 1; //0:left 1:center 2:right
        doc.Add(pg0);

        //本文開始 產生表格 -- START       
        PdfPTable pdfTab = new PdfPTable(new float[] { 1, 1, 1, 1, 1 }); // 建立5個欄位表格之相對寬度
        pdfTab.TotalWidth = 520f; // 表格總寬            
        pdfTab.LockedWidth = true;
        // 塞入資料 -------------------------- START         
        //-----第一列
        PdfPCell pc = new PdfPCell(new Phrase("戶名", ChFont));
        pc.MinimumHeight = 25;
        pc.HorizontalAlignment = 1; //0:left 1:center 2:right
        pdfTab.AddCell(pc); //寫入第1列第1個cell   
        pc = new PdfPCell(new Phrase("身份證字號", ChFont));
        pc.HorizontalAlignment = 1; //0:left 1:center 2:right
        pdfTab.AddCell(pc); //寫入第1列第2個cell    
        pc = new PdfPCell(new Phrase("立帳行庫（號）", ChFont));
        pc.HorizontalAlignment = 1; //0:left 1:center 2:right
        pdfTab.AddCell(pc); //寫入第1列第3個cell    
        pc = new PdfPCell(new Phrase("存簿帳號", ChFont));
        pc.HorizontalAlignment = 1; //0:left 1:center 2:right
        pdfTab.AddCell(pc); //寫入第1列第4個cell     
        pc = new PdfPCell(new Phrase("存款金額", ChFont));
        pc.HorizontalAlignment = 1; //0:left 1:center 2:right
        pdfTab.AddCell(pc); //寫入第1列第5個cell  

        //-----第二~二十四列(共25筆資料)
        for (int i = 2; i <= 26; i++)
        {
            pc = new PdfPCell(new Phrase(" " + (i - 1), ChFont)); pc.HorizontalAlignment = 1; //0:left 1:center 2:right
            pc.MinimumHeight = 20;
            pdfTab.AddCell(pc); //第1個cell   
            pc = new PdfPCell(new Phrase(" ", ChFont)); pc.HorizontalAlignment = 1; //0:left 1:center 2:right
            pdfTab.AddCell(pc); //第2個cell    
            pc = new PdfPCell(new Phrase(" ", ChFont)); pc.HorizontalAlignment = 1; //0:left 1:center 2:right
            pdfTab.AddCell(pc); //第3個cell    
            pc = new PdfPCell(new Phrase(" ", ChFont)); pc.HorizontalAlignment = 1; //0:left 1:center 2:right
            pdfTab.AddCell(pc); //第4個cell     
            pc = new PdfPCell(new Phrase(" ", ChFont)); pc.HorizontalAlignment = 1; //0:left 1:center 2:right
            pdfTab.AddCell(pc); //第5個cell         
        }
        //-----第二十五列
        pc = new PdfPCell(new Phrase("合計", ChFont)); pc.HorizontalAlignment = 1; //0:left 1:center 2:right
        pdfTab.AddCell(pc); //第1個cell   
        pc = new PdfPCell(new Phrase("", ChFont)); pc.HorizontalAlignment = 1; //0:left 1:center 2:right
        pdfTab.AddCell(pc); //第2個cell    
        pc = new PdfPCell(new Phrase("", ChFont)); pc.HorizontalAlignment = 1; //0:left 1:center 2:right
        pdfTab.AddCell(pc); //第3個cell    
        pc = new PdfPCell(new Phrase("", ChFont)); pc.HorizontalAlignment = 1; //0:left 1:center 2:right
        pdfTab.AddCell(pc); //第4個cell     
        pc = new PdfPCell(new Phrase("", ChFont)); pc.HorizontalAlignment = 1; //0:left 1:center 2:right
        pdfTab.AddCell(pc); //第5個cell  
        doc.Add(pdfTab); //將資料寫進文件中         
        // 塞入資料 -------------------------- END



        //#######Gen BarCode#############        
        PdfContentByte cb = pdfw.DirectContent;
        Paragraph pa = new Paragraph();
        Barcode128 barcode = new Barcode128();
        barcode.CodeType = Barcode.CODE128_UCC;
        barcode.Code = "0917241559";
        barcode.StartStopText = true;
        iTextSharp.text.Image barcodeImage = barcode.CreateImageWithBarcode(cb, null, null);
        barcodeImage.ScalePercent(110f);
        barcodeImage.Alignment = Element.ALIGN_BOTTOM;

        pa.Add(barcodeImage);
        doc.Add(pa);

        doc.Close();
        HttpContext.Current.Response.Clear();
        HttpContext.Current.Response.AddHeader("Content-Disposition", "attachment;filename=YilanExportPDF_" + DateTime.Now.Month.ToString().PadLeft(2, '0') + DateTime.Now.Day.ToString().PadLeft(2, '0') + ".pdf");
        HttpContext.Current.Response.ContentType = "application/octer-stream";
        HttpContext.Current.Response.OutputStream.Write(memory.GetBuffer(), 0, memory.GetBuffer().Length);
        HttpContext.Current.Response.OutputStream.Flush();
        HttpContext.Current.Response.OutputStream.Close();
        HttpContext.Current.Response.Flush();

    }
    //######################台銀舊報表區End#####################################
    public string getBatchNum(string sn) //取得【批號】10碼
    {
        YuMingClass.DataFunc myfunc = new DataFunc();
        Dictionary<string, object> Dic = new Dictionary<string, object>();
        Dic["@sn"] = sn;
        string BatchNum = "";
        string no = myfunc.DBGetOneRec("ComRemitConnectionString", "select top 1 BatchNum from RemitedList where ConSno=@sn", Dic); //現存批號
        //string today = (DateTime.Now.Year - 1911).ToString().Substring(1, 2) + DateTime.Now.Month.ToString().PadLeft(2, '0') + DateTime.Now.Day.ToString().PadLeft(2, '0');
        //DateTime cashDate = Convert.ToDateTime(myfunc.DBGetOneRec("ComRemitConnectionString", "select top 1 CashDate from RemitedList where ConSno=@sn", Dic)); //預計匯款日
        DateTime cashDate = DateTime.Now; //列印報表當日       
        string sCashDate = (cashDate.Year - 1911).ToString().Substring(1, 2) + cashDate.Month.ToString().PadLeft(2, '0') + cashDate.Day.ToString().PadLeft(2, '0');

        if (no.Equals("")) //若尚無批號，則給預設批號
        {
            string maxno = myfunc.DBGetOneRec("ComRemitConnectionString", "select max(BatchNum) from RemitedList where left(BatchNum,6)='" + sCashDate + "'");
            if (maxno.Equals("")) //是否有當日批號，若無就取預設批號
                BatchNum = (cashDate.Year - 1911).ToString().Substring(1, 2) + cashDate.Month.ToString().PadLeft(2, '0') + cashDate.Day.ToString().PadLeft(2, '0') + "6101";//批號(BatchNumber): 060821(製表日期:民國年月日)、 61(固定:登打系統 61~65)、 01流水號(01~99，00不可使用)
                                                                                                                                                                            // BatchNum = (DateTime.Now.Year - 1911).ToString().Substring(1, 2) + DateTime.Now.Month.ToString().PadLeft(2, '0') + DateTime.Now.Day.ToString().PadLeft(2, '0') + "6101";//批號(BatchNumber): 060821(製表日期:民國年月日)、 61(固定:登打系統 61~65)、 01流水號(01~99，00不可使用)
            else //若當日已有批號，則現在批號+1
            {
                if (maxno.Substring(8, 2).Equals("99"))
                {
                    BatchNum = "已達當日最大批號，請聯絡系統管理者";
                    return BatchNum;
                }
                else
                    BatchNum = (Convert.ToInt32(maxno) + 1).ToString().PadLeft(10, '0');
            }
            Dic["@BatchNum"] = BatchNum;
            myfunc.DBGetOneRec("ComRemitConnectionString", "update RemitedList set BatchNum=@BatchNum where ConSno=@sn", Dic); //更新批號
        }
        else
            BatchNum = no;

        return BatchNum;
    }

    public string getConMemo(string sn) //取得【用途說明】
    {
        YuMingClass.DataFunc myfunc = new DataFunc();
        Dictionary<string, object> Dic = new Dictionary<string, object>();
        Dic["@sn"] = sn;
        string rtn = myfunc.DBGetOneRec("ComRemitConnectionString", "select top 1 ConMemo from RemitedList where ConSno=@sn", Dic); //用途說明

        return rtn;
    }
    //######################產生庫款系統匯出檔區Start###############################
    public void genFinacailTxtFile(string sn)
    {
        string fileLoc = HttpContext.Current.Server.MapPath(@"~\ExportTxt\files");
        //string filename = "Finacail_" + sn.PadLeft(6, '0') + ".txt";
        string filename = "CR"+(DateTime.Now.Year-1911) + sn.PadLeft(5, '0') + ".txt";
        string content = "";
        FileStream fs = new FileStream(fileLoc + "\\" + filename, FileMode.Create);


        YuMingClass.DataFunc myfunc = new DataFunc();
        Dictionary<string, object> Dic = new Dictionary<string, object>();
        Dic["@sn"] = sn;
        DataTable dt = myfunc.DBGetDataTable("ComRemitConnectionString", "select ConSno as Consn,  CollectNo as No, CollecAcc as Acc, CollecName as name,RemitPrice as money,ifFee, ConMemo as memo from RemitedList where ConSno=@sn", Dic); //用途說明
        int i = 0;
        while (i < dt.Rows.Count)
        {
            if (content.Equals(""))
                content = "CR" + (DateTime.Now.Year - 1911) + dt.Rows[i][0].ToString() + ',' + dt.Rows[i][1] + ',' + dt.Rows[i][2].ToString().Replace("-", "") + ',' + dt.Rows[i][3] + ',' + dt.Rows[i][4] + ',' + dt.Rows[i][5].ToString().Trim() + ',' + dt.Rows[i][6].ToString().Trim();
            else
                content = content + "\r\n" + "CR" + (DateTime.Now.Year - 1911) + dt.Rows[i][0].ToString() + ',' + dt.Rows[i][1] + ',' + dt.Rows[i][2].ToString().Replace("-","") + ',' + dt.Rows[i][3] + ',' + dt.Rows[i][4] + ',' + dt.Rows[i][5].ToString().Trim() + ',' + dt.Rows[i][6].ToString().Trim();
            i++;
        }
        //獲得位元組陣列
        byte[] data = System.Text.Encoding.Default.GetBytes(content);
        //開始寫入
        fs.Write(data, 0, data.Length);
        //清空緩衝區、關閉流
        fs.Flush();
        fs.Close();
        //宣告並建立WebClient物件
        WebClient wc = new WebClient();
        //載入要下載的檔案
        byte[] downloadfile = wc.DownloadData(fileLoc + "\\" + filename);
        //清除Response內的HTML
        HttpContext.Current.Response.Clear();
        //設定標頭檔資訊 attachment 是本文章的關鍵字
        HttpContext.Current.Response.AddHeader("Content-Disposition", "attachment;filename=" + filename);
        //開始輸出讀取到的檔案
        HttpContext.Current.Response.BinaryWrite(downloadfile);
        //一定要加入這一行，否則會持續把Web內的HTML文字也輸出。
        HttpContext.Current.Response.End();
    }
    //==========郵局匯出檔內容==============
    //######################郵局台銀匯出檔區Start###############################
    public void genTxtFile(string kind, string sn)
    {
        string fileLoc = HttpContext.Current.Server.MapPath(@"~\ExportTxt\files");
        string filename = "CR" + sn.PadLeft(6, '0') + "_" + kind + ".txt";
        string content = "";
        FileStream fs = new FileStream(fileLoc + "\\" + filename, FileMode.Create);
        if (kind.Trim().Equals("POST"))
            content = getPostTxtContent(sn);
        else
            content = getTwBankTxtContent(sn);
        //獲得位元組陣列
        byte[] data = System.Text.Encoding.Default.GetBytes(content);
        //開始寫入
        fs.Write(data, 0, data.Length);
        //清空緩衝區、關閉流
        fs.Flush();
        fs.Close();
        //string downloadurl = "/ExportTxt/files/" + filename;

        //宣告並建立WebClient物件
        WebClient wc = new WebClient();
        //載入要下載的檔案
        byte[] downloadfile = wc.DownloadData(fileLoc + "\\" + filename);
        //清除Response內的HTML
        HttpContext.Current.Response.Clear();
        //設定標頭檔資訊 attachment 是本文章的關鍵字
        HttpContext.Current.Response.AddHeader("Content-Disposition", "attachment;filename=" + filename);
        //開始輸出讀取到的檔案
        HttpContext.Current.Response.BinaryWrite(downloadfile);
        //一定要加入這一行，否則會持續把Web內的HTML文字也輸出。
        HttpContext.Current.Response.End();
    }
    //==========郵局匯出檔內容==============
    public string getPostTxtContent(string sn)
    {
        YuMingClass.DataFunc myfunc = new DataFunc();
        Dictionary<string, object> Dic = new Dictionary<string, object>();
        Dic["@sn"] = sn;
        string rtn = "";
        string applymoney = ""; //帳款金額(8位數)
        string CashDate = ""; //匯款日期(7位數)
        DataTable dt = myfunc.DBGetDataTable("ComRemitConnectionString", "SELECT * from RemitedList where ConSno=@sn", Dic);
        int i = 0;
        string sid; //身份證字號
        while (i < dt.Rows.Count)
        {
            sid = dt.Rows[i]["CollectId"].ToString().ToUpper().Trim();
            if (sid.Length == 8)
                sid = sid.ToString().PadRight(10, ' ');
            if (i == 0)
            {
                DateTime tmpDate = Convert.ToDateTime(dt.Rows[0]["CashDate"]);
                CashDate = (tmpDate.Year - 1911) + tmpDate.Month.ToString().PadLeft(2, '0') + tmpDate.Day.ToString().PadLeft(2, '0');
            }
            applymoney = dt.Rows[i]["RemitPrice"].ToString().PadLeft(8, '0');
            if (rtn.Equals(""))
                rtn = "**********" + "      " + "********" + CashDate + dt.Rows[i]["CollecAcc"].ToString().Replace("-", "") + sid + applymoney + "00" + "               ";
            else
                rtn = rtn + "\r\n" + "**********" + "      " + "********" + CashDate + dt.Rows[i]["CollecAcc"].ToString().Replace("-", "") + sid + applymoney + "00" + "               ";
            i++;
        }
        return rtn;
    }
    //==========台銀匯出檔內容==============
    public string getTwBankTxtContent(string sn)
    {
        YuMingClass.DataFunc myfunc = new DataFunc();
        Dictionary<string, object> Dic = new Dictionary<string, object>();
        Dic["@sn"] = sn;
        string rtn = "";
        int applymoney; //帳款金額(6位數)
        string flowNum = "00000"; //首筆流水號(5位數)固定
        string cashDate = ""; //匯款日(6位數)
        string filler1 = "                       ";//空白欄位(23位數)固定
        string remitName = ""; //匯款人姓名(68碼)
        string filler2 = "                                                                ";//空白欄位(64位數)固定
        string conOrga = ""; //彙整人單位(機關)
        int totalAmount = 0; //總筆數
        int totalCost = 0; //總金額
        string memo = ""; //備註

        //~~~~~~~~~~~首筆資料:
        string firstRow = "1022";//前4碼固定
        string batchhNum = getBatchNum(sn);//批號(10碼)

        DataTable dt = myfunc.DBGetDataTable("ComRemitConnectionString", "SELECT * from RemitedList where ConSno=@sn", Dic);
        //~~~~~~~~~~~明細資料:
        string contentRow = "";
        while (totalAmount < dt.Rows.Count)
        {
            if (totalAmount == 0)
            {
                Nullable<DateTime> tmpDate = null;

                try
                {
                    tmpDate = Convert.ToDateTime(dt.Rows[0]["ConDate"]);
                }
                catch (Exception)
                {
                }
                if (tmpDate == null)
                    tmpDate = Convert.ToDateTime(dt.Rows[0]["CashDate"]);

                //DateTime tmpDate = Convert.ToDateTime(dt.Rows[0]["CashDate"]);
                //cashDate = (tmpDate.Year - 1911).ToString().Substring(1, 2) + tmpDate.Month.ToString().PadLeft(2, '0') + tmpDate.Day.ToString().PadLeft(2, '0');
                cashDate = batchhNum.Substring(0, 6);
                Dic["@organum"] = dt.Rows[0]["ConUnit"].ToString().Substring(0, 3);
                conOrga = myfunc.DBGetOneRec("personConnectionString", "select orname from orgat where ornum=@organum", Dic);
                remitName = CHT_WordPadLeftRight(("宜府" + conOrga), "L", 68, ' ');
                firstRow = firstRow + batchhNum + flowNum + cashDate + filler1 + remitName + filler2;
            }
            //第2筆開始之資料
            remitName = CHT_WordPadLeftRight(dt.Rows[totalAmount]["CollecName"].ToString(), "L", 68, ' ');
            //要判斷是否有手續費
            if (dt.Rows[totalAmount]["IfFee"].ToString().Trim().Equals("是"))
                applymoney = Convert.ToInt32(dt.Rows[totalAmount]["RemitPrice"].ToString().PadLeft(6, '0')) - 30;
            else
                applymoney = Convert.ToInt32(dt.Rows[totalAmount]["RemitPrice"].ToString().PadLeft(6, '0'));
            memo = dt.Rows[totalAmount]["RemitMemo"].ToString();
            byte[] byteStr = Encoding.GetEncoding("big5").GetBytes(memo); //把string轉為byte (讓中文字的長度算為2bit)             
            if (byteStr.Length >= 50)
                memo = SubStr(memo, 0, 50);
            else
                memo = CHT_WordPadLeftRight(memo, "L", 50, ' ');

            if (contentRow.Equals(""))
                contentRow = "2022" + batchhNum + (totalAmount + 1).ToString().PadLeft(5, ' ') + cashDate + dt.Rows[totalAmount]["CollectNo"].ToString().Trim() + "11" + dt.Rows[totalAmount]["CollecAcc"].ToString().Replace("-", "").PadLeft(14, '0') + remitName + (applymoney + "00").PadLeft(14, ' ') + memo;
            else
                contentRow += "\r\n" + "2022" + batchhNum + (totalAmount + 1).ToString().PadLeft(5, ' ') + cashDate + dt.Rows[totalAmount]["CollectNo"].ToString().Trim() + "11" + dt.Rows[totalAmount]["CollecAcc"].ToString().Replace("-", "").PadLeft(14, '0') + remitName + (applymoney + "00").PadLeft(14, ' ') + memo;
            totalCost += applymoney;
            totalAmount++;
        }
        //~~~~~~~~~~~尾筆資料:
        string lastRow = "3022";//前4碼固定
        string filler3 = "                                                                                                                                         ";//固定137碼
        lastRow += batchhNum + "99999" + cashDate + totalAmount.ToString().PadLeft(4, ' ') + (totalCost.ToString() + "00").PadLeft(14, ' ') + filler3;
        rtn = firstRow + "\r\n" + contentRow + "\r\n" + lastRow;
        //組合之完整資料
        //rtn = firstRow + rtn + lastRow;
        return rtn;
    }
    /// <param name="org">原始字串</param>
    /// <param name="RL">靠右靠左補 R(靠右) L(靠左)</param>
    /// <param name="sLen">長度</param>
    /// <param name="padStr">替代字元</param>
    string CHT_WordPadLeftRight(string org, string RL, int sLen, char padStr)
    {
        var sResult = "";
        //計算轉換過實際的總長
        int orgLen = 0;
        int tLen = 0;
        for (int i = 0; i < org.Length; i++)
        {
            string s = org.Substring(i, 1);
            int vLen = 0;
            //判斷 asc 表是否介於 0~128
            if (Convert.ToInt32(s[0]) > 128 || Convert.ToInt32(s[0]) < 0)
            {
                vLen = 2;
            }
            else
            {
                vLen = 1;
            }
            orgLen += vLen;
            if (orgLen > sLen)
            {
                orgLen -= vLen;
                break;
            }
            sResult += s;
        }
        //計算轉換過後，最後實際的長度
        tLen = sLen - (orgLen - org.Length);
        if (RL == "R")
        {
            return sResult.PadLeft(tLen, padStr);
        }
        else
        {
            return sResult.PadRight(tLen, padStr);
        }
    }
    public string SubStr(string a_SrcStr, int a_StartIndex, int a_Cnt)
    {
        Encoding l_Encoding = Encoding.GetEncoding("big5", new EncoderExceptionFallback(), new DecoderReplacementFallback(""));
        byte[] l_byte = l_Encoding.GetBytes(a_SrcStr);
        if (a_Cnt <= 0)
            return "";
        //例若長度10 
        //若a_StartIndex傳入9 -> ok, 10 ->不行 
        if (a_StartIndex + 1 > l_byte.Length)
            return "";
        else
        {
            //若a_StartIndex傳入9 , a_Cnt 傳入2 -> 不行 -> 改成 9,1 
            if (a_StartIndex + a_Cnt > l_byte.Length)
                a_Cnt = l_byte.Length - a_StartIndex;
        }
        return l_Encoding.GetString(l_byte, a_StartIndex, a_Cnt);
    }
    static string[] getConInf(string id)
    {
        YuMingClass.DataFunc myfunc = new DataFunc();
        Dictionary<string, object> Dic = new Dictionary<string, object>();
        Dic["@Acc"] = id;
        string[] str = new string[3];
        str[0] = myfunc.DBGetOneRec("personConnectionString", "select Username FROM vperson where Account=@Acc", Dic);
        str[1] = myfunc.DBGetOneRec("personConnectionString", "select SID FROM vperson where Account=@Acc", Dic);
        str[2] = myfunc.DBGetOneRec("personConnectionString", "select tel2+'-'+tel3 FROM vperson where Account=@Acc", Dic);
        return str;
    }
    //######################郵局台銀匯出檔區End#####################################
}