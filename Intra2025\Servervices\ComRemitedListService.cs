using Intra2025.Data;
using Intra2025.Models.ComRemit;
using Microsoft.EntityFrameworkCore;
using QuestPDF.Fluent;
using QuestPDF.Helpers;
using QuestPDF.Infrastructure;
using QuestPDF.Previewer;
using QuestPDF.Drawing;
using QuestPDF.Elements;

namespace Intra2025.Servervices
{
    public class ComRemitedListService
    {
        private readonly ComRemitDbContext _context;
        private readonly ComRemitQRCodeService _barcodeService;

        public ComRemitedListService(ComRemitDbContext context, ComRemitQRCodeService barcodeService)
        {
            _context = context;
            _barcodeService = barcodeService;
        }

        public async Task<(List<RemitedListViewModel> Items, int TotalPages)> GetPagedListAsync(string keyword, int pageIndex, int pageSize)
        {
            // 只顯示已完成彙整的資料（CashDate 不為 null）
            var query = _context.RemitedList.Where(r => r.CashDate != null).AsQueryable();

            // 合併搜尋（系統編號/收款人戶名/用途說明）
            if (!string.IsNullOrWhiteSpace(keyword))
            {
                // 嘗試解析為系統編號
                if (int.TryParse(keyword, out var conSnoInt))
                {
                    query = query.Where(r => r.ConSno == conSnoInt ||
                                           (r.CollecName ?? "").Contains(keyword) ||
                                           (r.RemitMemo ?? "").Contains(keyword));
                }
                else
                {
                    // 純文字搜尋（收款人戶名/用途說明）
                    query = query.Where(r => (r.CollecName ?? "").Contains(keyword) || (r.RemitMemo ?? "").Contains(keyword));
                }
            }
            var group = query.GroupBy(r => r.ConSno)
                .Select(g => new RemitedListViewModel
                {
                    ConSno = g.Key,
                    Count = g.Count(),
                    TotalAmount = g.Sum(x => (decimal)(x.RemitPrice ?? 0)),
                    RemitMemo = g.Select(x => x.RemitMemo).FirstOrDefault(),
                    ConDate = g.Select(x => x.ConDate).FirstOrDefault(),
                    ConMemo = g.Select(x => x.ConMemo).FirstOrDefault(), // 使用正確的 ConMemo 欄位
                    CashDate = g.Select(x => x.CashDate).FirstOrDefault() // 添加 CashDate 用於排序
                });
            var totalCount = await group.CountAsync();
            var totalPages = (int)Math.Ceiling(totalCount / (double)pageSize);
            var items = await group.OrderByDescending(x => x.CashDate ?? x.ConDate) // 優先按 CashDate 排序，如果沒有則用 ConDate
                .ThenByDescending(x => x.ConSno) // 次要排序條件
                .Skip((pageIndex - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            // 為每個項目載入前5筆明細
            foreach (var item in items)
            {
                if (item.ConSno.HasValue)
                {
                    item.Details = await GetDetailListAsync(item.ConSno.Value);
                }
            }

            return (items, totalPages);
        }

        public async Task<List<RemitedListDetailViewModel>> GetDetailListAsync(int conSno)
        {
            return await _context.RemitedList
                .Where(r => r.ConSno == conSno)
                .OrderBy(r => r.Sno)
                .Take(5)
                .Select(r => new RemitedListDetailViewModel
                {
                    CollecName = r.CollecName,
                    CollecAcc = r.CollecAcc,
                    RemitPrice = r.RemitPrice,
                    CollectNo = r.CollectNo
                })
                .ToListAsync();
        }

        public async Task DeleteByConSnoAsync(int conSno)
        {
            var items = await _context.RemitedList.Where(r => r.ConSno == conSno).ToListAsync();
            if (items.Any())
            {
                _context.RemitedList.RemoveRange(items);
                await _context.SaveChangesAsync();
            }
        }

        // 新增缺少的方法
        public async Task<List<Intra2025.Models.ComRemit.RemitedList>> GetRemitedListByConSnoAsync(int conSno)
        {
            return await _context.RemitedList
                .Where(r => r.ConSno == conSno)
                .OrderBy(r => r.Sno)
                .ToListAsync();
        }

        public async Task<bool> UpdateCashDateAsync(int conSno, DateTime cashDate)
        {
            try
            {
                var items = await _context.RemitedList.Where(r => r.ConSno == conSno).ToListAsync();
                foreach (var item in items)
                {
                    item.CashDate = cashDate;
                }
                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<List<RemitedListViewModel>> GetRemitedListAsync()
        {
            var result = await GetPagedListAsync("", 1, 1000);
            return result.Items;
        }

        public async Task<List<RemitedListViewModel>> SearchRemitedListAsync(string keyword)
        {
            var result = await GetPagedListAsync(keyword, 1, 1000);
            return result.Items;
        }

        // 根據登入帳號取得匯款記錄 (權限控制 - 使用 ConPer 欄位)
        public async Task<List<RemitedListViewModel>> GetRemitedListByUserAccountAsync(string userAccount)
        {
            var result = await GetPagedListByUserAccountAsync("", 1, 1000, userAccount);
            return result.Items;
        }

        // 根據登入帳號和關鍵字搜尋匯款記錄 (權限控制 - 使用 ConPer 欄位)
        public async Task<List<RemitedListViewModel>> SearchRemitedListByUserAccountAsync(string keyword, string userAccount)
        {
            var result = await GetPagedListByUserAccountAsync(keyword, 1, 1000, userAccount);
            return result.Items;
        }

        // 帶用戶權限控制的分頁查詢方法 (使用 ConPer 欄位過濾)
        public async Task<(List<RemitedListViewModel> Items, int TotalPages)> GetPagedListByUserAccountAsync(string keyword, int pageIndex, int pageSize, string userAccount)
        {
            if (string.IsNullOrWhiteSpace(userAccount))
            {
                return (new List<RemitedListViewModel>(), 0);
            }

            var query = _context.RemitedList.Where(r => r.ConPer == userAccount);

            if (!string.IsNullOrWhiteSpace(keyword))
            {
                if (int.TryParse(keyword, out int conSno))
                {
                    // 數字搜尋（整彙編號）
                    query = query.Where(r => r.ConSno == conSno);
                }
                else
                {
                    // 純文字搜尋（收款人戶名/用途說明）
                    query = query.Where(r => (r.CollecName ?? "").Contains(keyword) || (r.RemitMemo ?? "").Contains(keyword));
                }
            }

            var group = query.GroupBy(r => r.ConSno)
                .Select(g => new RemitedListViewModel
                {
                    ConSno = g.Key,
                    Count = g.Count(),
                    TotalAmount = g.Sum(x => (decimal)(x.RemitPrice ?? 0)),
                    RemitMemo = g.Select(x => x.RemitMemo).FirstOrDefault(),
                    ConDate = g.Select(x => x.ConDate).FirstOrDefault(),
                    ConMemo = g.Select(x => x.ConMemo).FirstOrDefault(),
                    CashDate = g.Select(x => x.CashDate).FirstOrDefault()
                });

            var totalCount = await group.CountAsync();
            var totalPages = (int)Math.Ceiling(totalCount / (double)pageSize);
            var items = await group.OrderByDescending(x => x.CashDate ?? x.ConDate)
                .ThenByDescending(x => x.ConSno)
                .Skip((pageIndex - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            // 為每個項目載入前5筆明細 (也需要權限控制)
            foreach (var item in items)
            {
                if (item.ConSno.HasValue)
                {
                    item.Details = await GetDetailListByUserAccountAsync(item.ConSno.Value, userAccount);
                }
            }

            return (items, totalPages);
        }

        // 帶用戶權限控制的明細查詢方法 (使用 ConPer 欄位過濾)
        public async Task<List<RemitedListDetailViewModel>> GetDetailListByUserAccountAsync(int conSno, string userAccount)
        {
            if (string.IsNullOrWhiteSpace(userAccount))
            {
                return new List<RemitedListDetailViewModel>();
            }

            return await _context.RemitedList
                .Where(r => r.ConSno == conSno && r.ConPer == userAccount)
                .OrderBy(r => r.Sno)
                .Take(5)
                .Select(r => new RemitedListDetailViewModel
                {
                    CollecName = r.CollecName,
                    CollecAcc = r.CollecAcc,
                    RemitPrice = r.RemitPrice,
                    CollectNo = r.CollectNo
                })
                .ToListAsync();
        }



        public async Task<bool> DeleteRemitedAsync(int conSno)
        {
            try
            {
                await DeleteByConSnoAsync(conSno);
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<(string Content, string Filename)> ExportTxtAsync(int conSno)
        {
            var items = await _context.RemitedList.Where(r => r.ConSno == conSno).OrderBy(r => r.Sno).ToListAsync();
            if (!items.Any()) return (string.Empty, string.Empty);

            // 判斷是否為郵局格式 (CollectNo 全部為 "7000021")
            var ifGenPostFile = items.All(r => r.CollectNo == "7000021");
            var searchId = conSno.ToString();

            // 根據 Fun.cs 的檔名規則
            string filename;
            if (ifGenPostFile)
            {
                filename = $"CR{searchId.PadLeft(6, '0')}_POST.txt";
            }
            else
            {
                filename = $"CR{searchId.PadLeft(6, '0')}_TWBANK.txt";
            }

            // 參考 Fun.cs 格式，這裡僅示範：每行「戶名,帳號,金額」
            var lines = items.Select(r => $"{r.CollecName},{r.CollecAcc},{r.RemitPrice}");
            var content = string.Join("\r\n", lines);

            return (content, filename);
        }

        public async Task<(byte[] PdfBytes, string Filename)> PrintPdfAsync(int conSno)
        {
            try
            {
                // 設定 QuestPDF 授權，確保關閉除錯模式
                QuestPDF.Settings.License = LicenseType.Community;
                QuestPDF.Settings.EnableDebugging = false;

                var items = await _context.RemitedList.Where(r => r.ConSno == conSno).OrderBy(r => r.Sno).ToListAsync();
                if (!items.Any())
                {
                    Console.WriteLine($"PrintPdfAsync: 找不到 ConSno={conSno} 的資料");
                    return (Array.Empty<byte>(), "");
                }

                // 判斷是否為郵局格式 (CollectNo 全部為 "7000021")
                var ifGenPostFile = items.All(r => r.CollectNo == "7000021");
                var searchId = conSno.ToString();

                Console.WriteLine($"PrintPdfAsync: ConSno={conSno}, 資料筆數={items.Count}, 是否郵局格式={ifGenPostFile}");

                byte[] pdfBytes;
                string filename;

                if (ifGenPostFile)
                {
                    Console.WriteLine("PrintPdfAsync: 生成郵局報表");
                    pdfBytes = GeneratePostalReportAdvanced(items, conSno);
                    filename = $"CR{searchId.PadLeft(6, '0')}_POST.pdf";
                }
                else
                {
                    Console.WriteLine("PrintPdfAsync: 生成台銀報表");
                    pdfBytes = GenerateTWBankReportAdvanced(items, conSno);
                    filename = $"CR{searchId.PadLeft(6, '0')}_TWBANK.pdf";
                }

                if (pdfBytes == null)
                {
                    Console.WriteLine("PrintPdfAsync: 警告 - PDF 生成返回 null");
                    return (Array.Empty<byte>(), "");
                }

                Console.WriteLine($"PrintPdfAsync: PDF 生成完成，大小={pdfBytes?.Length ?? 0} bytes");
                return (pdfBytes ?? Array.Empty<byte>(), filename ?? "");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"PrintPdfAsync 發生錯誤: {ex}");
                throw; // 重新拋出異常，讓調用方處理
            }
        }

        // 簡化的 PDF 生成方法，避免複雜的格式問題
        private byte[] GenerateSimpleReport(List<RemitedList> list, int conSno, bool isPostal)
        {
            var totalAmount = list.Count;
            var totalCost = list.Sum(x => (decimal)(x.RemitPrice ?? 0));
            var searchId = conSno.ToString();
            var filename = $"CR{searchId.PadLeft(6, '0')}_{(isPostal ? "POST" : "TWBANK")}";
            var memo = list.FirstOrDefault()?.RemitMemo ?? "";

            return Document.Create(container =>
            {
                container.Page(page =>
                {
                    page.Size(PageSizes.A4);
                    page.Margin(2, Unit.Centimetre);
                    page.DefaultTextStyle(x => x.FontFamily("Microsoft YaHei").FontSize(12));

                    page.Content().Column(column =>
                    {
                        column.Item().Text(isPostal ? "委託郵局代存員工薪資總表" : "整批匯款資料清單")
                            .FontSize(18).Bold().AlignCenter();
                        column.Item().PaddingVertical(20);

                        column.Item().Text($"檔名: {filename}");
                        column.Item().Text($"撥存總額: {totalCost:N0}");
                        column.Item().Text($"總筆數: {totalAmount}");
                        column.Item().Text($"備註: {memo}");

                        column.Item().PaddingVertical(20);

                        // 簡化的明細列表
                        column.Item().Table(table =>
                        {
                            table.ColumnsDefinition(columns =>
                            {
                                columns.ConstantColumn(50);   // 序號
                                columns.RelativeColumn(2);   // 戶名
                                columns.ConstantColumn(100); // 帳號
                                columns.ConstantColumn(80);  // 金額
                            });

                            table.Cell().Element(HeaderCellStyle).Text("序號");
                            table.Cell().Element(HeaderCellStyle).Text("戶名");
                            table.Cell().Element(HeaderCellStyle).Text("帳號");
                            table.Cell().Element(HeaderCellStyle).Text("金額");

                            for (int i = 0; i < list.Count; i++)
                            {
                                var item = list[i];
                                table.Cell().Element(BorderCellStyle).Text((i + 1).ToString());
                                table.Cell().Element(BorderCellStyle).Text(item.CollecName ?? "");
                                table.Cell().Element(BorderCellStyle).Text(item.CollecAcc ?? "");
                                table.Cell().Element(BorderCellStyle).AlignRight().Text($"{(item.RemitPrice ?? 0):N0}");
                            }
                        });
                    });

                    page.Footer().AlignCenter().Text(text =>
                    {
                        text.Span($"{filename} - ");
                        text.CurrentPageNumber();
                        text.Span(" / ");
                        text.TotalPages();
                    });
                });
            }).GeneratePdf();
        }

        private byte[] GeneratePostalReportAdvanced(List<RemitedList> list, int conSno)
        {
            // 使用簡化版本避免複雜的格式問題
            return GenerateSimpleReport(list, conSno, true);
        }

        private byte[] GenerateTWBankReportAdvanced(List<RemitedList> data, int conSno)
        {
            // 使用簡化版本避免複雜的格式問題
            return GenerateSimpleReport(data, conSno, false);
        }

        private static string GenerateBatchNumber()
        {
            var now = DateTime.Now;
            return $"{now.Year % 100:D2}{now.Month:D2}{now.Day:D2}001";
        }

        private static IContainer BorderCellStyle(IContainer container)
        {
            return container
                .Border(0.3f)
                .BorderColor(Colors.Black)
                .PaddingVertical(1)
                .PaddingHorizontal(1)
                .AlignCenter()
                .AlignMiddle()
                .DefaultTextStyle(x => x.FontSize(9));
        }

        private static IContainer HeaderCellStyle(IContainer container)
        {
            return container
                .Border(0.3f)
                .BorderColor(Colors.Black)
                .Background(Colors.White)
                .PaddingVertical(3)
                .PaddingHorizontal(2)
                .AlignCenter()
                .AlignMiddle();
        }
    }
}
