using Intra2025.Data;
using Intra2025.Models.ComRemit;
using Microsoft.EntityFrameworkCore;

namespace Intra2025.Servervices
{
    public class ComRemitPayeeService
    {
        private readonly ComRemitDbContext _context;

        public ComRemitPayeeService(ComRemitDbContext context)
        {
            _context = context;
        }

        // 取得所有收款人資料
        public async Task<List<Payee>> GetAllPayeesAsync()
        {
            try
            {
                return await _context.Payee
                    .OrderBy(p => p.CollectNo)
                    .ThenBy(p => p.CollecAcc)
                    .ToListAsync();
            }
            catch
            {
                return new List<Payee>();
            }
        }

        // 根據登入帳號取得收款人資料 (權限控制)
        public async Task<List<Payee>> GetPayeesByUserAccountAsync(string userAccount)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(userAccount))
                {
                    return new List<Payee>();
                }

                var result = await _context.Payee
                    .Where(p => p.BelongAcc == userAccount)
                    .OrderBy(p => p.CollectNo)
                    .ThenBy(p => p.CollecAcc)
                    .ToListAsync();

                return result;
            }
            catch (Exception ex)
            {
                // 記錄錯誤但不拋出異常，返回空列表
                return new List<Payee>();
            }
        }

        // 根據收款人戶名搜尋
        public async Task<List<Payee>> SearchPayeesByNameAsync(string name)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(name))
                {
                    return await GetAllPayeesAsync();
                }

                return await _context.Payee
                    .Where(p => p.CollectName != null && p.CollectName.Contains(name))
                    .OrderBy(p => p.CollectNo)
                    .ThenBy(p => p.CollecAcc)
                    .ToListAsync();
            }
            catch
            {
                return new List<Payee>();
            }
        }

        // 根據登入帳號和收款人戶名搜尋 (權限控制)
        public async Task<List<Payee>> SearchPayeesByNameAndUserAccountAsync(string name, string userAccount)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(userAccount))
                {
                    return new List<Payee>();
                }

                var query = _context.Payee.Where(p => p.BelongAcc == userAccount);

                if (!string.IsNullOrWhiteSpace(name))
                {
                    query = query.Where(p => p.CollectName != null && p.CollectName.Contains(name));
                }

                return await query
                    .OrderBy(p => p.CollectNo)
                    .ThenBy(p => p.CollecAcc)
                    .ToListAsync();
            }
            catch
            {
                return new List<Payee>();
            }
        }

        // 取得單筆收款人資料
        public Task<Payee?> GetPayeeByIdAsync(int id)
        {
            // 由於現在使用複合主鍵，這個方法需要重新實作
            // 暫時返回null，建議使用GetPayeeByKeyAsync方法
            return Task.FromResult<Payee?>(null);
        }

        // 根據複合主鍵取得收款人資料
        public async Task<Payee?> GetPayeeByKeyAsync(string collectNo, string collecAcc)
        {
            try
            {
                return await _context.Payee.FindAsync(collectNo, collecAcc);
            }
            catch
            {
                return null;
            }
        }

        // 根據複合主鍵取得收款人資料 (別名方法)
        public async Task<Payee?> GetPayeeByKeysAsync(string collectNo, string collecAcc)
        {
            return await GetPayeeByKeyAsync(collectNo, collecAcc);
        }

        // 新增收款人資料
        public async Task<bool> AddPayeeAsync(Payee payee)
        {
            try
            {
                _context.Payee.Add(payee);
                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        // 更新收款人資料
        public async Task<bool> UpdatePayeeAsync(Payee payee)
        {
            try
            {
                _context.Payee.Update(payee);
                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        // 刪除收款人資料
        public Task<bool> DeletePayeeAsync(int id)
        {
            // 由於使用複合主鍵，這個方法需要重新實作
            return Task.FromResult(false);
        }

        // 根據複合主鍵刪除收款人資料
        public async Task<bool> DeletePayeeByKeyAsync(string collectNo, string collecAcc)
        {
            try
            {
                var payee = await _context.Payee.FindAsync(collectNo, collecAcc);
                if (payee == null)
                    return false;

                _context.Payee.Remove(payee);
                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        // 根據複合主鍵刪除收款人資料 (重載方法)
        public async Task<bool> DeletePayeeAsync(string collectNo, string collecAcc)
        {
            return await DeletePayeeByKeyAsync(collectNo, collecAcc);
        }

        // 檢查資料庫連接
        public async Task<bool> TestConnectionAsync()
        {
            try
            {
                await _context.Database.CanConnectAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        // 取得收款人總數
        public async Task<int> GetTotalPayeesAsync()
        {
            try
            {
                return await _context.Payee.CountAsync();
            }
            catch
            {
                return 0;
            }
        }

        // 檢查帳號是否已存在
        public async Task<bool> IsAccountExistsAsync(string collectNo, string collecAcc, int? excludeId = null)
        {
            try
            {
                return await _context.Payee
                    .Where(p => p.CollectNo == collectNo && p.CollecAcc == collecAcc)
                    .AnyAsync();
            }
            catch
            {
                return false;
            }
        }

        // 檢查帳號是否已存在 (重載方法，無excludeId參數)
        public Task<bool> IsAccountExistsAsync(string collectNo, string collecAcc)
        {
            return IsAccountExistsAsync(collectNo, collecAcc, null);
        }

        public async Task<List<string>> GetPayeeNameSuggestionsAsync(string searchTerm)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
                return new List<string>();

            return await _context.Payee
                .Where(p => p.CollectName != null && p.CollectName.Contains(searchTerm))
                .Select(p => p.CollectName!)
                .Distinct()
                .Take(10)
                .ToListAsync();
        }

        public async Task<Payee?> GetPayeeByNameAsync(string name)
        {
            if (string.IsNullOrWhiteSpace(name))
                return null;

            var payee = await _context.Payee
                .FirstOrDefaultAsync(p => p.CollectName == name);

            if (payee != null && !string.IsNullOrEmpty(payee.CollectNo))
            {
                payee.FinancialName = await GetFinancialNameByNoAsync(payee.CollectNo);
            }

            return payee;
        }

        public async Task<Payee?> GetPayeeByNameAndAccountAsync(string name, string account)
        {
            if (string.IsNullOrWhiteSpace(name) || string.IsNullOrWhiteSpace(account))
                return null;

            var payee = await _context.Payee
                .FirstOrDefaultAsync(p => p.CollectName == name && p.CollecAcc == account);

            if (payee != null && !string.IsNullOrEmpty(payee.CollectNo))
            {
                payee.FinancialName = await GetFinancialNameByNoAsync(payee.CollectNo);
            }

            return payee;
        }

        public async Task<string> GetFinancialNameByNoAsync(string financialNo)
        {
            if (string.IsNullOrWhiteSpace(financialNo))
                return "";

            var financial = await _context.Financial
                .FirstOrDefaultAsync(f => f.Code == financialNo);

            return financial?.Name ?? "";
        }
    }
}
