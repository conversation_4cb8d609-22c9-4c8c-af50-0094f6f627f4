using Intra2025.Data;
using Intra2025.Components;
using Intra2025.Servervices;
using Intra2025.Components.Base;
using Microsoft.AspNetCore.DataProtection;
using Microsoft.AspNetCore.HttpOverrides;
using System.Net;
using Microsoft.EntityFrameworkCore;
using QuestPDF.Infrastructure;


var builder = WebApplication.CreateBuilder(args);

// 設定 QuestPDF 授權
QuestPDF.Settings.License = LicenseType.Community;

// �K�[��x���ѵ{��
builder.Logging.ClearProviders();
builder.Logging.AddConsole();


builder.Services.AddDataProtection()
    .PersistKeysToFileSystem(new DirectoryInfo(@"C:\keys"))  // ���w���_���s���m
    .SetApplicationName("BBSR_Intra");

// �p�G�z�ݭn���� SSL ���� (�ȫ�ĳ�b�}�o/�������ҤU�ϥ�)
var handler = new HttpClientHandler();
handler.ServerCertificateCustomValidationCallback = (message, cert, chain, errors) => true;

builder.Services.AddScoped(sp => new HttpClient(handler)
{
    BaseAddress = new Uri("https://************")
});

// Add services to the container.
builder.Services.AddRazorComponents()
    .AddInteractiveServerComponents();

builder.Services.AddDbContext<AppDbContext>(options =>
{
    options.UseSqlServer(builder.Configuration.GetConnectionString("LocalConn") ?? throw new InvalidOperationException("Connection is not found "));
});
builder.Services.AddDbContext<ReportDbContext>(options =>
{
    options.UseSqlServer(builder.Configuration.GetConnectionString("ReportConn") ?? throw new InvalidOperationException("Connection is not found "));
});

builder.Services.AddDbContext<YCRSDbContext>(options =>
{
    options.UseSqlServer(builder.Configuration.GetConnectionString("YCRSConn") ?? throw new InvalidOperationException("Connection is not found "));
});

builder.Services.AddDbContext<ComRemitDbContext>(options =>
{
    options.UseSqlServer(builder.Configuration.GetConnectionString("LocalConn") ?? throw new InvalidOperationException("Connection is not found "));
});

builder.Services.AddSingleton<LaunchSettingsService>();

// �K�[ HttpClient
builder.Services.AddHttpClient();
builder.Services.AddScoped(sp => new HttpClient());
//builder.Services.AddTransient<SSO>();
// ���U EmailService
builder.Services.AddScoped<EmailService>();

// 註冊 ComRemit 相關服務
builder.Services.AddScoped<ComRemitPayeeService>();
builder.Services.AddScoped<ComRemitService>();
builder.Services.AddScoped<ComRemitedListService>();
builder.Services.AddScoped<ComRemitQRCodeService>();

// �K�[ Session �䴩
builder.Services.AddDistributedMemoryCache(); // �ϥΤ��s�Ӧs�x session
builder.Services.AddSession(options =>
{
    options.IdleTimeout = TimeSpan.FromMinutes(120); // �]�w Session �L���ɶ� -> 120��
    options.Cookie.HttpOnly = true; // ���\�� Http �X��
    options.Cookie.IsEssential = true; // ��� GDPR �Ҽ{
});

builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll", policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyHeader()
              .AllowAnyMethod()
              .SetIsOriginAllowed(_ => true)
              .AllowCredentials();
    });
});

builder.Services.Configure<ForwardedHeadersOptions>(options =>
{
    options.ForwardedHeaders = ForwardedHeaders.XForwardedFor | ForwardedHeaders.XForwardedProto;

    // �p�G�z�����A����󤺳������A���T���w���H�����N�z���A���d��
    options.KnownProxies.Add(IPAddress.Parse("127.0.0.1")); // ���a�N�z
});
// ���U ClientIpService �� DI �e��
builder.Services.AddSingleton<ClientIpService>();  // �ϥ� Singleton�A�o�˾�����ε{���i�H�@�ɦP�@�ӪA�ȹ��

builder.Services.AddScoped<UserState>(); // ������ DI �e����*/
builder.Services.AddScoped<SsoService>();
builder.Services.AddMemoryCache();

builder.Services.AddHttpContextAccessor();

builder.Services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();

var allConns = builder.Configuration.GetSection("ConnectionStrings").GetChildren();
foreach (var conn in allConns)
{
    Console.WriteLine($"{conn.Key}: {conn.Value}");
}

var app = builder.Build();

// ���U�����h
app.UseMiddleware<ClientIpMiddleware>();

// Configure the HTTP request pipeline.
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Error", createScopeForErrors: true);
    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}

app.UseHttpsRedirection();

app.UseSession();

// ��� ILogger ���
var logger = app.Services.GetRequiredService<ILogger<Program>>();

// �˴� PathBase
var configuration = builder.Configuration;
var pathBase = configuration["PathBase"];
if (!string.IsNullOrEmpty(pathBase))
{
    app.UsePathBase(pathBase);
}
app.UseStaticFiles();
app.UseAntiforgery();

app.MapRazorComponents<App>()
    .AddInteractiveServerRenderMode();

app.Run();
