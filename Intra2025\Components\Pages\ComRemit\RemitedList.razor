@page "/ComRemit/RemitedList"
@rendermode @(new InteractiveServerRenderMode(prerender: false))
@using Intra2025.Models.ComRemit
@using Intra2025.Servervices
@using Intra2025.Components.Base
@using Microsoft.AspNetCore.Components.Web
@inject ComRemitedListService RemitedListService
@inject IJSRuntime JSRuntime
@inherits BasePageComponent

<PageTitle>已彙整清單</PageTitle>

<h3>【已彙整清單(Step3)】</h3>

<div class="row mb-3">
    <div class="col-md-4">
        <input type="text" class="form-control" placeholder="系統編號/收款人戶名/用途說明搜尋" @bind="searchKeyword"
            @onkeypress="OnKeyPress" />
    </div>
    <div class="col-md-2">
        <button class="btn btn-primary" @onclick="Search">搜尋</button>
         <button class="btn btn-secondary" @onclick="ClearSearch">清除</button>
    </div>
</div>

@if (!string.IsNullOrEmpty(errorMessage))
{
    <div class="alert alert-danger">@errorMessage</div>
}

@if (remitedList != null)
{
    <table class="table table-hover" style="border: 1px solid #e9ecef; border-collapse: collapse;">
        <thead>
            <tr style="background-color: #f8f9fa;">
                <th style="border: 1px solid #dee2e6; padding: 10px; font-weight: 600; color: #495057;">系統編號</th>
                <th style="border: 1px solid #dee2e6; padding: 10px; font-weight: 600; color: #495057;">筆數/申請金額</th>
                <th style="border: 1px solid #dee2e6; padding: 10px; font-weight: 600; color: #495057;">前5筆明細</th>
                <th style="border: 1px solid #dee2e6; padding: 10px; font-weight: 600; color: #495057;">用途說明/彙整時間</th>
                <th style="border: 1px solid #dee2e6; padding: 10px; font-weight: 600; color: #495057;width:125px;align:center">操作</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var item in remitedList)
            {
                <tr>
                    <td style="border: 1px solid #e9ecef; padding: 12px; vertical-align: top;">@item.ConSno</td>
                    <td style="border: 1px solid #e9ecef; padding: 12px; vertical-align: top;">共 @item.Count 筆 <br />
                        @item.TotalAmount.ToString("F0") </td>
                    <td style="border: 1px solid #e9ecef; padding: 12px; vertical-align: top;">
                        @if (item.Details != null && item.Details.Any())
                        {
                            <table class="table table-sm" style="margin-bottom: 0;font-size:12px; border: 1px solid #c4f5d2;">
                                <thead>
                                    <tr style="background-color: #f8f9fa;">
                                        <th
                                            style="border: 1px solid #c4f5d2; padding: 8px; font-size: 0.799rem; font-weight: 500; background-color:#c4f5d2; width: 50px;">
                                            序號</th>
                                        <th
                                            style="border: 1px solid #c4f5d2; padding: 8px; font-size: 0.799rem; font-weight: 500; background-color:#c4f5d2;">
                                            收款人戶名</th>
                                        <th
                                            style="border: 1px solid #c4f5d2; padding: 8px; font-size: 0.799rem; font-weight: 500; background-color:#c4f5d2; width: 80px;">
                                            行號</th>
                                        <th
                                            style="border: 1px solid #c4f5d2; padding: 8px; font-size: 0.799rem; font-weight: 500; background-color:#c4f5d2;">
                                            帳號</th>
                                        <th
                                            style="border: 1px solid #c4f5d2; padding: 8px; font-size: 0.799rem; font-weight: 500; background-color:#c4f5d2;">
                                            金額</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @{
                                        int rowIndex = 1;
                                    }
                                    @foreach (var d in item.Details)
                                    {
                                        <tr>
                                            <td style="border: 1px solid #c4f5d2; padding: 6px; font-size: 0.799rem; text-align: center;">@rowIndex</td>
                                            <td style="border: 1px solid #c4f5d2; padding: 6px; font-size: 0.799rem;">@d.CollecName</td>
                                            <td style="border: 1px solid #c4f5d2; padding: 6px; font-size: 0.799rem; text-align: center;">@d.CollectNo</td>
                                            <td style="border: 1px solid #c4f5d2; padding: 6px; font-size: 0.799rem;">@d.CollecAcc</td>
                                            <td style="border: 1px solid #c4f5d2; padding: 6px; font-size: 0.799rem;">@d.RemitPrice</td>
                                        </tr>
                                        rowIndex++;
                                    }
                                </tbody>
                            </table>
                        }
                        else
                        {
                            <span class="text-muted">無明細資料</span>
                        }
                    </td>
                    <td style="border: 1px solid #e9ecef; padding: 12px; vertical-align: top;">@item.ConMemo <br /> 【
                        @item.ConDate 】</td>
                    <td style="border: 1px solid #e9ecef; padding: 12px; vertical-align: top;">
                        <button class="btn btn-info btn-sm" @onclick="() => Print(item.ConSno ?? 0)">列印</button>
                        <button class="btn btn-success btn-sm" @onclick="() => Export(item.ConSno ?? 0)">匯出</button>
                        <button class="btn btn-secondary btn-sm" @onclick="() => Copy(item.ConSno ?? 0)">複製</button>
                        <button class="btn btn-danger btn-sm" @onclick="() => Delete(item.ConSno ?? 0)">刪除</button>
                    </td>
                </tr>
            }
        </tbody>
    </table>
}
else
{
    <div class="text-center">
        <div class="spinner-border" role="status">
            <span class="visually-hidden">載入中...</span>
        </div>
        <p>載入中...</p>
    </div>
}

@code {
    private List<RemitedListViewModel>? remitedList;
    private string searchKeyword = "";
    private string errorMessage = "";

    protected override async Task OnInitializedAsync()
    {
        // SSO 驗證已在 BasePageComponent 中自動執行
        await base.OnInitializedAsync();
        await LoadData();
    }

    private async Task LoadData()
    {
        try
        {
            errorMessage = "";

            // 檢查用戶是否已登入
            if (_userState == null || string.IsNullOrEmpty(_userState.Account))
            {
                errorMessage = "用戶未登入或帳號資訊不完整";
                Logger.LogError("用戶未登入，停止載入RemitedList資料");
                return;
            }

            // 根據登入用戶帳號載入資料 (權限控制 - 使用 ConPer 欄位)
            Logger.LogInformation($"開始執行 GetRemitedListByUserAccountAsync，用戶帳號: {_userState.Account}");
            remitedList = await RemitedListService.GetRemitedListByUserAccountAsync(_userState.Account);
            Logger.LogInformation($"GetRemitedListByUserAccountAsync 完成，共 {remitedList?.Count ?? 0} 筆資料");
        }
        catch (Exception ex)
        {
            errorMessage = $"載入資料時發生錯誤: {ex.Message}";
            Logger.LogError(ex, "載入RemitedList資料時發生錯誤");
        }
    }

    private async Task Search()
    {
        try
        {
            errorMessage = "";

            // 檢查用戶是否已登入
            if (_userState == null || string.IsNullOrEmpty(_userState.Account))
            {
                errorMessage = "用戶未登入或帳號資訊不完整";
                Logger.LogError("用戶未登入，停止搜尋RemitedList資料");
                return;
            }

            if (string.IsNullOrWhiteSpace(searchKeyword))
            {
                await LoadData();
            }
            else
            {
                // 根據登入用戶帳號搜尋資料 (權限控制 - 使用 ConPer 欄位)
                Logger.LogInformation($"開始執行 SearchRemitedListByUserAccountAsync，用戶帳號: {_userState.Account}，關鍵字: {searchKeyword}");
                remitedList = await RemitedListService.SearchRemitedListByUserAccountAsync(searchKeyword, _userState.Account);
                Logger.LogInformation($"SearchRemitedListByUserAccountAsync 完成，共 {remitedList?.Count ?? 0} 筆資料");
            }
        }
        catch (Exception ex)
        {
            errorMessage = $"搜尋時發生錯誤: {ex.Message}";
            Logger.LogError(ex, "搜尋RemitedList時發生錯誤");
        }
    }

    private async Task ClearSearch()
    {
        searchKeyword = "";
        await LoadData();
    }

    private async Task OnKeyPress(KeyboardEventArgs e)
    {
        if (e.Key == "Enter")
        {
            await Search();
        }
    }

    private async Task Print(int conSno)
    {
        try
        {
            var result = await RemitedListService.PrintPdfAsync(conSno);
            if (result.PdfBytes != null && result.PdfBytes.Length > 0)
            {
                // 將 PDF 轉換為 Base64 並在新視窗中開啟
                var base64 = Convert.ToBase64String(result.PdfBytes);
                var dataUrl = $"data:application/pdf;base64,{base64}";
                await JS.InvokeVoidAsync("open", dataUrl, "_blank");
            }
            else
            {
                await JS.InvokeVoidAsync("alert", "無法產生PDF檔案");
            }
        }
        catch (Exception ex)
        {
            await JS.InvokeVoidAsync("alert", $"列印失敗: {ex.Message}");
            Logger.LogError(ex, "列印PDF時發生錯誤");
        }
    }

    private async Task Export(int conSno)
    {
        try
        {
            var result = await RemitedListService.PrintPdfAsync(conSno);
            if (result.PdfBytes != null && result.PdfBytes.Length > 0)
            {
                // 觸發下載
                var base64 = Convert.ToBase64String(result.PdfBytes);
                await JS.InvokeVoidAsync("downloadFile", result.Filename, base64);
            }
            else
            {
                await JS.InvokeVoidAsync("alert", "無法產生PDF檔案");
            }
        }
        catch (Exception ex)
        {
            await JS.InvokeVoidAsync("alert", $"匯出失敗: {ex.Message}");
            Logger.LogError(ex, "匯出PDF時發生錯誤");
        }
    }

    private async Task Copy(int conSno)
    {
        try
        {
            // 複製功能 - 這裡可以實作複製到剪貼簿或其他邏輯
            await JS.InvokeVoidAsync("alert", $"複製功能尚未實作 (ConSno: {conSno})");
        }
        catch (Exception ex)
        {
            await JS.InvokeVoidAsync("alert", $"複製失敗: {ex.Message}");
        }
    }

    private async Task Delete(int conSno)
    {
        try
        {
            var confirmed = await JS.InvokeAsync<bool>("confirm", "確定要刪除這筆資料嗎？");
            if (confirmed)
            {
                var success = await RemitedListService.DeleteRemitedAsync(conSno);
                if (success)
                {
                    await JS.InvokeVoidAsync("alert", "刪除成功！");
                    await LoadData(); // 重新載入資料
                }
                else
                {
                    await JS.InvokeVoidAsync("alert", "刪除失敗！");
                }
            }
        }
        catch (Exception ex)
        {
            await JS.InvokeVoidAsync("alert", $"刪除失敗: {ex.Message}");
            Logger.LogError(ex, "刪除RemitedList時發生錯誤");
        }
    }
}
